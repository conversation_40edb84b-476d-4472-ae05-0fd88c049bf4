# TechSupport Pro - NPM Scripts Documentation

Complete reference for all available npm scripts in the TechSupport Pro project. This guide explains what each script does, when to use it, and provides usage examples.

## 📋 Quick Reference

```bash
# Development
npm run dev              # Start development server
npm run build            # Build for production
npm run preview          # Preview production build
npm run clean            # Clean build directory
npm run serve            # Build and serve

# Code Quality
npm run lint             # Check JavaScript for errors
npm run lint:fix         # Fix JavaScript errors automatically
npm run format           # Format code with Prettier
npm run format:check     # Check code formatting

# Testing
npm test                 # Run unit tests
npm run test:watch       # Run tests in watch mode
npm run a11y-test        # Run accessibility tests
npm run lighthouse       # Run performance audit

# Optimization
npm run optimize-images  # Optimize images for web
npm run generate-sitemap # Generate XML sitemap
```

## 🔧 Development Scripts

### **`npm run dev`**
**Purpose**: Starts the Vite development server with hot module replacement (HMR)

**What it does**:
- Starts development server on `http://localhost:3000`
- Enables hot module replacement for instant updates
- Compiles SCSS to CSS on-the-fly
- Serves static assets from `public/` directory
- Provides source maps for debugging

**Usage**:
```bash
npm run dev

# With custom port
npm run dev -- --port 3001

# With specific host
npm run dev -- --host 0.0.0.0

# Open browser automatically
npm run dev -- --open
```

**Output**:
```
  VITE v5.0.0  ready in 234 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
  ➜  press h to show help
```

### **`npm run build`**
**Purpose**: Creates optimized production build

**What it does**:
- Compiles and minifies JavaScript
- Processes and optimizes CSS
- Optimizes images and assets
- Generates asset hashes for cache busting
- Creates `dist/` directory with production files
- Generates source maps for debugging

**Usage**:
```bash
npm run build

# Build with custom output directory
npm run build -- --outDir custom-dist

# Build without minification (debugging)
npm run build -- --minify false

# Build with detailed output
npm run build -- --logLevel info
```

**Output**:
```
✓ built in 1.23s
dist/index.html                   2.45 kB │ gzip:  1.32 kB
dist/assets/main-a1b2c3d4.js     45.67 kB │ gzip: 15.23 kB
dist/assets/main-e5f6g7h8.css     12.34 kB │ gzip:  3.45 kB
```

### **`npm run preview`**
**Purpose**: Serves the production build locally for testing

**What it does**:
- Serves files from `dist/` directory
- Starts server on `http://localhost:4173`
- Simulates production environment
- Useful for testing before deployment

**Usage**:
```bash
npm run preview

# With custom port
npm run preview -- --port 4174

# With specific host
npm run preview -- --host 0.0.0.0
```

**Prerequisites**: Must run `npm run build` first

### **`npm run clean`**
**Purpose**: Removes the build directory and temporary files

**What it does**:
- Deletes `dist/` directory
- Removes build artifacts
- Clears Vite cache
- Prepares for fresh build

**Usage**:
```bash
npm run clean

# Clean and rebuild
npm run clean && npm run build
```

### **`npm run serve`**
**Purpose**: Builds the project and immediately serves it

**What it does**:
- Runs `npm run build`
- Then runs `npm run preview`
- One-command build and serve

**Usage**:
```bash
npm run serve

# Equivalent to:
npm run build && npm run preview
```

## ✅ Code Quality Scripts

### **`npm run lint`**
**Purpose**: Checks JavaScript code for errors and style issues

**What it does**:
- Runs ESLint on all JavaScript files in `src/`
- Checks for syntax errors
- Enforces coding standards
- Reports accessibility issues
- Identifies potential bugs

**Usage**:
```bash
npm run lint

# Lint specific file
npm run lint -- src/scripts/main.js

# Lint with specific format
npm run lint -- --format table

# Lint and show warnings
npm run lint -- --max-warnings 0
```

**Output**:
```
✓ No ESLint errors found
  2 warnings found:
  
src/scripts/components/Navigation.js
  15:10  warning  'unused' is defined but never used  no-unused-vars
  23:5   warning  Missing JSDoc comment               require-jsdoc
```

### **`npm run lint:fix`**
**Purpose**: Automatically fixes JavaScript code issues

**What it does**:
- Runs ESLint with `--fix` flag
- Automatically fixes auto-fixable issues
- Reports remaining issues that need manual fixing
- Saves fixed files automatically

**Usage**:
```bash
npm run lint:fix

# Fix specific file
npm run lint:fix -- src/scripts/main.js

# Fix and show what was changed
npm run lint:fix -- --fix-dry-run
```

**Output**:
```
✓ 8 problems fixed automatically
  2 problems remaining (manual fix required)
```

### **`npm run format`**
**Purpose**: Formats code using Prettier

**What it does**:
- Formats JavaScript, CSS, HTML, and SCSS files
- Ensures consistent code style
- Fixes indentation, spacing, and line breaks
- Applies project formatting rules

**Usage**:
```bash
npm run format

# Format specific file type
npm run format -- "src/**/*.js"

# Format with custom config
npm run format -- --config .prettierrc.custom
```

**File types formatted**:
- JavaScript (`.js`)
- CSS (`.css`)
- HTML (`.html`)
- SCSS (`.scss`)

### **`npm run format:check`**
**Purpose**: Checks if code is properly formatted without making changes

**What it does**:
- Verifies code formatting without modifying files
- Reports files that need formatting
- Useful for CI/CD pipelines
- Returns exit code 1 if formatting issues found

**Usage**:
```bash
npm run format:check

# Check specific files
npm run format:check -- "src/scripts/**/*.js"
```

**Output**:
```
Checking formatting...
✓ All files are properly formatted

# Or if issues found:
✗ Code style issues found in 3 files:
  src/scripts/main.js
  src/styles/main.scss
  src/index.html
```

## 🧪 Testing Scripts

### **`npm test`**
**Purpose**: Runs the complete test suite

**What it does**:
- Executes all Jest unit tests
- Runs tests in `tests/` directory
- Generates test coverage report
- Reports test results and statistics

**Usage**:
```bash
npm test

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test -- Navigation.test.js

# Run tests matching pattern
npm test -- --testNamePattern="should toggle"

# Run tests in specific directory
npm test -- tests/unit/

# Verbose output
npm test -- --verbose
```

**Output**:
```
 PASS  tests/unit/Navigation.test.js
 PASS  tests/unit/I18nService.test.js
 PASS  tests/unit/dom.test.js

Test Suites: 3 passed, 3 total
Tests:       15 passed, 15 total
Snapshots:   0 total
Time:        2.847s
Coverage:    85.4% statements, 78.2% branches, 90.1% functions, 84.8% lines
```

### **`npm run test:watch`**
**Purpose**: Runs tests in watch mode for development

**What it does**:
- Watches for file changes
- Re-runs tests automatically when files change
- Interactive mode with options
- Useful during development

**Usage**:
```bash
npm run test:watch

# Watch specific test file
npm run test:watch -- Navigation.test.js

# Watch with coverage
npm run test:watch -- --coverage
```

**Interactive commands**:
- `a` - Run all tests
- `f` - Run only failed tests
- `p` - Filter by filename pattern
- `t` - Filter by test name pattern
- `q` - Quit watch mode

### **`npm run a11y-test`**
**Purpose**: Runs accessibility tests using Pa11y

**What it does**:
- Tests all pages defined in sitemap
- Checks WCAG 2.1 AA compliance
- Reports accessibility violations
- Generates detailed accessibility report

**Usage**:
```bash
npm run a11y-test

# Test specific URL
npx pa11y http://localhost:4173

# Test with custom standard
npx pa11y http://localhost:4173 --standard WCAG2AAA

# Generate HTML report
npx pa11y-ci --sitemap http://localhost:4173/sitemap.xml --reporter html
```

**Prerequisites**: Development server must be running (`npm run preview`)

**Output**:
```
Running Pa11y on 5 URLs:
✓ http://localhost:4173/ - 0 errors
✓ http://localhost:4173/services.html - 0 errors
✓ http://localhost:4173/contact.html - 0 errors

All tests passed!
```

### **`npm run lighthouse`**
**Purpose**: Runs Lighthouse performance audit

**What it does**:
- Audits performance, accessibility, best practices, SEO
- Generates detailed HTML report
- Measures Core Web Vitals
- Provides optimization recommendations

**Usage**:
```bash
npm run lighthouse

# Audit specific categories
lighthouse http://localhost:4173 --only-categories=performance,accessibility

# Mobile audit
lighthouse http://localhost:4173 --preset=mobile

# Custom output
lighthouse http://localhost:4173 --output=json --output-path=./report.json
```

**Prerequisites**: Production build must be served (`npm run preview`)

**Output**:
```
Lighthouse report generated: ./reports/lighthouse.html

Performance: 97
Accessibility: 100
Best Practices: 96
SEO: 100
```

## 🎨 Optimization Scripts

### **`npm run optimize-images`**
**Purpose**: Optimizes images for web performance

**What it does**:
- Converts images to WebP and AVIF formats
- Compresses images without quality loss
- Generates responsive image variants
- Maintains original files as backup

**Usage**:
```bash
npm run optimize-images

# Optimize specific directory
node tools/optimize-images.js --input src/assets/images/gallery

# Custom quality settings
node tools/optimize-images.js --quality 85

# Skip AVIF generation
node tools/optimize-images.js --no-avif
```

**Supported formats**:
- Input: PNG, JPG, JPEG
- Output: WebP, AVIF (with original fallbacks)

**Output**:
```
Optimizing images...
✓ hero-image.jpg → hero-image.webp (65% smaller)
✓ hero-image.jpg → hero-image.avif (78% smaller)
✓ logo.png → logo.webp (45% smaller)

Processed 15 images, saved 2.3 MB total
```

### **`npm run generate-sitemap`**
**Purpose**: Generates XML sitemap for SEO

**What it does**:
- Scans project for HTML files
- Generates XML sitemap
- Sets appropriate priority and change frequency
- Saves to `public/sitemap.xml`

**Usage**:
```bash
npm run generate-sitemap

# Custom base URL
node tools/generate-sitemap.js --baseUrl https://techsupportpro.nl

# Include specific pages only
node tools/generate-sitemap.js --pages index,services,contact

# Custom output location
node tools/generate-sitemap.js --output dist/sitemap.xml
```

**Output**:
```
Generating sitemap...
✓ Found 8 pages
✓ Generated sitemap.xml
✓ Sitemap saved to public/sitemap.xml

Pages included:
- / (priority: 1.0)
- /services.html (priority: 0.8)
- /contact.html (priority: 0.7)
- /about.html (priority: 0.6)
```

## 🔄 Workflow Examples

### **Development Workflow**
```bash
# Start development
npm run dev

# In another terminal, run tests in watch mode
npm run test:watch

# Check code quality before committing
npm run lint && npm run format:check

# Run full test suite
npm test
```

### **Pre-Deployment Workflow**
```bash
# Clean previous build
npm run clean

# Optimize assets
npm run optimize-images
npm run generate-sitemap

# Check code quality
npm run lint && npm run format:check

# Run tests
npm test

# Build for production
npm run build

# Test production build
npm run preview

# Run performance audit
npm run lighthouse

# Run accessibility tests
npm run a11y-test
```

### **CI/CD Pipeline**
```bash
# Install dependencies
npm ci

# Code quality checks
npm run lint
npm run format:check

# Run tests with coverage
npm test -- --coverage

# Build project
npm run build

# Run accessibility tests
npm run a11y-test

# Deploy dist/ directory
```

---

**📜 This script documentation ensures efficient development workflows and maintains high code quality standards throughout the project lifecycle.**
