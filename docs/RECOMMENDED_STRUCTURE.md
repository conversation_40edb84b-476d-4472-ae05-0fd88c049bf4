# TechSupport Pro - Project Structure Guide

This document outlines the current project structure and explains the purpose of each directory and file. This structure follows modern web development best practices and is designed for scalability, maintainability, and performance.

## 📁 Complete Project Structure

```
techsupport-pro/
├── src/                          # 🎯 Source files (development)
│   ├── assets/                   # 🖼️ Static assets
│   │   ├── images/              # Image files
│   │   │   ├── optimized/        # WebP, AVIF optimized formats
│   │   │   ├── original/         # Source images (PNG, JPG)
│   │   │   ├── icons/            # SVG icons and favicons
│   │   │   ├── flags/            # Country flag icons for i18n
│   │   │   ├── it-support/       # IT support related images
│   │   │   ├── logo/             # Brand logos and variations
│   │   │   └── patterns/         # Background patterns and textures
│   │   ├── fonts/                # Local font files (WOFF2, WOFF)
│   │   └── videos/               # Video assets (if needed)
│   │
│   ├── styles/                   # 🎨 SCSS organization (7-1 architecture)
│   │   ├── abstracts/            # Configuration and helpers
│   │   │   ├── _variables.scss   # SCSS variables (colors, fonts, spacing)
│   │   │   ├── _mixins.scss      # Reusable SCSS mixins
│   │   │   └── _functions.scss   # SCSS functions (calculations, utilities)
│   │   ├── base/                 # Foundation styles
│   │   │   ├── _reset.scss       # CSS reset/normalize
│   │   │   ├── _typography.scss  # Font definitions and text styles
│   │   │   └── _base.scss        # Basic HTML element styles
│   │   ├── components/           # Reusable UI components
│   │   │   ├── _buttons.scss     # Button styles and variations
│   │   │   ├── _cards.scss       # Card component styles
│   │   │   ├── _forms.scss       # Form elements and validation
│   │   │   └── _modals.scss      # Modal dialog styles
│   │   ├── layout/               # Layout-specific styles
│   │   │   ├── _header.scss      # Site header and branding
│   │   │   ├── _footer.scss      # Site footer
│   │   │   ├── _navigation.scss  # Navigation menus and mobile
│   │   │   └── _grid.scss        # Grid system and containers
│   │   ├── pages/                # Page-specific styles
│   │   │   ├── _home.scss        # Homepage specific styles
│   │   │   ├── _services.scss    # Services page styles
│   │   │   ├── _contact.scss     # Contact page styles
│   │   │   └── _blog.scss        # Blog page styles
│   │   ├── themes/               # Theme variations
│   │   │   ├── _light.scss       # Light theme (default)
│   │   │   └── _dark.scss        # Dark theme (future)
│   │   ├── utilities/            # Helper classes
│   │   │   ├── _spacing.scss     # Margin and padding utilities
│   │   │   ├── _text.scss        # Text alignment and styling
│   │   │   └── _display.scss     # Display and visibility utilities
│   │   └── main.scss             # 📋 Main stylesheet entry point
│   │
│   ├── scripts/                  # ⚡ JavaScript modules (ES6+)
│   │   ├── components/           # UI component classes
│   │   │   ├── Navigation.js     # Mobile navigation and dropdowns
│   │   │   ├── ContactForm.js    # Contact form with validation
│   │   │   ├── LanguageSwitcher.js # i18n language switching
│   │   │   └── FAQ.js            # FAQ accordion functionality
│   │   ├── services/             # Business logic and data services
│   │   │   ├── I18nService.js    # Internationalization service
│   │   │   ├── ValidationService.js # Form validation logic
│   │   │   ├── ContactService.js # Contact form submission
│   │   │   ├── AnalyticsService.js # User analytics tracking
│   │   │   ├── NotificationService.js # User notifications
│   │   │   └── PWAService.js     # Progressive Web App features
│   │   ├── utils/                # Utility functions and helpers
│   │   │   ├── dom.js            # DOM manipulation utilities
│   │   │   └── performance.js    # Performance monitoring
│   │   ├── config/               # Configuration files
│   │   │   ├── constants.js      # Application constants
│   │   │   └── environment.js    # Environment-specific config
│   │   └── main.js               # 🚀 Application entry point
│   │
│   ├── templates/                # 📄 HTML templates and components
│   │   ├── components/           # Reusable HTML components
│   │   │   ├── header.html       # Site header template
│   │   │   ├── footer.html       # Site footer template
│   │   │   ├── navigation.html   # Navigation menu template
│   │   │   └── service-card.html # Service card component
│   │   ├── layouts/              # Page layout templates
│   │   │   ├── base.html         # Base layout template
│   │   │   ├── page.html         # Standard page layout
│   │   │   └── blog.html         # Blog post layout
│   │   └── pages/                # Individual page templates
│   │       ├── index.html        # Homepage template
│   │       ├── services.html     # Services page template
│   │       ├── contact.html      # Contact page template
│   │       └── about.html        # About page template
│   │
│   ├── data/                     # 📊 Static data files
│   │   ├── services.json         # Service offerings data
│   │   ├── testimonials.json     # Customer testimonials
│   │   ├── team.json             # Team member information
│   │   └── i18n/                 # Internationalization files
│   │       ├── nl.json           # Dutch translations
│   │       ├── en.json           # English translations
│   │       ├── fr.json           # French translations
│   │       └── pt.json           # Portuguese translations
│   │
│   ├── content/                  # 📝 Content management
│   │   ├── blog/                 # Blog posts (Markdown format)
│   │   │   ├── 2024-01-15-ransomware-protection.md
│   │   │   └── 2024-02-01-network-security.md
│   │   └── pages/                # Static page content (Markdown)
│   │
│   └── index.html                # 🏠 Main HTML entry point
│
├── public/                       # 🌐 Public assets (copied as-is to dist)
│   ├── robots.txt               # Search engine crawling directives
│   ├── sitemap.xml              # SEO sitemap (auto-generated)
│   ├── site.webmanifest         # PWA manifest file
│   └── sw.js                    # Service Worker for offline functionality
│
├── dist/                         # 📦 Built files (generated by Vite)
│   ├── assets/                  # Optimized and hashed assets
│   │   ├── css/                 # Minified CSS files
│   │   ├── js/                  # Bundled and minified JS
│   │   ├── images/              # Optimized images
│   │   └── fonts/               # Font files
│   ├── index.html               # Processed HTML files
│   └── sw.js                    # Service Worker
│
├── tests/                        # 🧪 Test files and configurations
│   ├── unit/                    # Unit tests for components/services
│   │   ├── dom.test.js          # DOM utility tests
│   │   └── i18n.test.js         # i18n service tests
│   ├── integration/             # Integration tests
│   ├── e2e/                     # End-to-end tests
│   └── setup.js                 # Jest test configuration
│
├── docs/                         # 📚 Project documentation
│   ├── SETUP.md                 # Development environment setup
│   ├── DEPLOYMENT.md            # Production deployment guide
│   ├── accessibility-guide.md   # Accessibility implementation guide
│   ├── ux-guidelines.md         # UX design guidelines
│   ├── MULTILANGUAGE_SYSTEM.md  # i18n system documentation
│   ├── API.md                   # JavaScript API documentation
│   ├── TESTING.md               # Testing strategies and procedures
│   ├── PERFORMANCE.md           # Performance optimization guide
│   ├── SCRIPTS.md               # NPM scripts documentation
│   └── RECOMMENDED_STRUCTURE.md # This file - project structure guide
│
├── tools/                        # 🔧 Build tools and automation scripts
│   ├── optimize-images.js       # Image optimization (WebP, AVIF)
│   └── generate-sitemap.js      # SEO sitemap generation
│
├── reports/                      # 📊 Generated reports and audits
│   ├── lighthouse/              # Lighthouse performance reports
│   ├── coverage/                # Test coverage reports
│   └── accessibility/           # Pa11y accessibility reports
│
├── node_modules/                 # 📦 Dependencies (auto-generated)
│
├── .github/                      # 🤖 GitHub workflows (CI/CD)
│   └── workflows/
│       ├── ci.yml               # Continuous integration
│       ├── deploy.yml           # Deployment automation
│       └── lighthouse.yml       # Performance monitoring
│
├── package.json                  # 📋 Project dependencies and scripts
├── package-lock.json            # Dependency lock file
├── vite.config.js               # Vite build configuration
├── jest.config.js               # Jest testing configuration
├── babel.config.js              # Babel transpilation config
├── postcss.config.js            # PostCSS processing config
├── lighthouserc.js              # Lighthouse CI configuration
├── .eslintrc.js                 # ESLint code quality rules
├── .prettierrc                  # Prettier formatting rules
├── .gitignore                   # Git ignore patterns
├── README.md                    # Project overview and setup
└── RECOMMENDED_STRUCTURE.md     # This file - project structure guide
```

## 🎯 Key Benefits of This Structure

### **1. 🏗️ Separation of Concerns**
- **Source vs. Built**: Clear distinction between development (`src/`) and production (`dist/`) files
- **Asset Organization**: Logical grouping of images, styles, scripts, and content
- **Configuration Isolation**: Build tools and configs separated from application code

### **2. 🧩 Modular Architecture**
- **Component-Based**: UI components can be developed, tested, and maintained independently
- **Service Layer**: Business logic separated from presentation layer
- **Utility Functions**: Reusable helpers organized by functionality
- **Plugin System**: Easy to extend with new components and services

### **3. 📈 Scalability**
- **Feature Addition**: New pages, components, and services can be added without restructuring
- **Team Collaboration**: Multiple developers can work on different parts without conflicts
- **Code Splitting**: Automatic bundling optimization for better performance
- **Asset Management**: Organized asset structure supports growth

### **4. 🔧 Maintainability**
- **Logical Organization**: Intuitive file placement makes code easy to find and modify
- **Consistent Naming**: Clear naming conventions across all directories
- **Documentation**: Each major directory has clear purpose and examples
- **Version Control**: Structure optimized for Git workflows and code reviews

### **5. ⚡ Build Optimization**
- **Automated Processing**: Images, CSS, and JS automatically optimized
- **Code Splitting**: Intelligent bundling for optimal loading performance
- **Asset Hashing**: Cache-busting for production deployments
- **Tree Shaking**: Unused code automatically removed

### **6. 🧪 Testing Strategy**
- **Test Types**: Dedicated spaces for unit, integration, and e2e tests
- **Test Organization**: Tests mirror source structure for easy navigation
- **Automation**: CI/CD integration for continuous testing
- **Coverage**: Comprehensive test coverage tracking

### **7. 📚 Documentation Structure**
- **Developer Guides**: Setup, deployment, and contribution guides
- **Technical Docs**: API documentation and architecture decisions
- **User Guides**: Accessibility and UX guidelines
- **Process Docs**: Testing strategies and performance optimization

## 🚀 Getting Started with This Structure

### **For New Developers**
1. **Read** the [SETUP.md](SETUP.md) for environment setup
2. **Explore** the `src/` directory to understand the codebase
3. **Check** the `docs/` directory for specific implementation guides
4. **Run** `npm run dev` to start the development server

### **For Adding New Features**
1. **Components**: Add to `src/scripts/components/` with corresponding styles in `src/styles/components/`
2. **Services**: Add business logic to `src/scripts/services/`
3. **Pages**: Create new pages in `src/` with styles in `src/styles/pages/`
4. **Tests**: Add tests in `tests/` mirroring the source structure

### **For Maintenance**
1. **Dependencies**: Update via `npm update` and test thoroughly
2. **Performance**: Monitor with `npm run lighthouse`
3. **Accessibility**: Validate with `npm run a11y-test`
4. **Documentation**: Keep docs updated with code changes

## 📋 Directory Purpose Quick Reference

| Directory | Purpose | Key Files |
|-----------|---------|-----------|
| `src/assets/` | Static assets (images, fonts) | Optimized images, SVG icons |
| `src/styles/` | SCSS stylesheets (7-1 pattern) | `main.scss`, component styles |
| `src/scripts/` | JavaScript modules | `main.js`, components, services |
| `src/data/` | Static data and translations | i18n JSON files |
| `public/` | Static files copied to dist | `robots.txt`, `sitemap.xml` |
| `tests/` | All test files | Unit, integration, e2e tests |
| `docs/` | Project documentation | Setup, deployment, guides |
| `tools/` | Build and automation scripts | Image optimization, sitemap |
| `dist/` | Production build output | Optimized, bundled files |

---

**This structure is designed to grow with your project while maintaining clarity and performance.**
