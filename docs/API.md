# TechSupport Pro - JavaScript API Documentation

Complete documentation for all JavaScript components, services, and utilities in the TechSupport Pro website.

## 📚 Table of Contents

- [Components](#-components)
  - [Navigation](#navigation)
  - [ContactForm](#contactform)
  - [LanguageSwitcher](#languageswitcher)
- [Services](#-services)
  - [I18nService](#i18nservice)
  - [ValidationService](#validationservice)
  - [ContactService](#contactservice)
  - [AnalyticsService](#analyticsservice)
  - [NotificationService](#notificationservice)
  - [PWAService](#pwaservice)
- [Utilities](#-utilities)
  - [DOM Utilities](#dom-utilities)
  - [Performance Utilities](#performance-utilities)
- [Main Application](#-main-application)

## 🧩 Components

### Navigation

**File**: `src/scripts/components/Navigation.js`

Handles mobile navigation, dropdown menus, and accessibility features.

#### Constructor
```javascript
const navigation = new Navigation(element, options);
```

**Parameters:**
- `element` (HTMLElement): Navigation container element
- `options` (Object): Configuration options

**Options:**
```javascript
{
  mobileBreakpoint: 768,        // Mobile breakpoint in pixels
  closeOnOutsideClick: true,    // Close dropdowns on outside click
  closeOnEscape: true           // Close dropdowns on Escape key
}
```

#### Methods

##### `init()`
Initializes the navigation component.
```javascript
navigation.init();
```

##### `toggleMobile()`
Toggles mobile navigation menu.
```javascript
navigation.toggleMobile();
```

##### `openMobile()`
Opens mobile navigation menu.
```javascript
navigation.openMobile();
```

##### `closeMobile()`
Closes mobile navigation menu.
```javascript
navigation.closeMobile();
```

##### `toggleDropdown(dropdown)`
Toggles a dropdown menu.
```javascript
const dropdown = document.querySelector('.nav-dropdown');
navigation.toggleDropdown(dropdown);
```

##### `setActive(href)`
Sets active navigation item.
```javascript
navigation.setActive('/services.html');
```

##### `destroy()`
Destroys the navigation component and removes event listeners.
```javascript
navigation.destroy();
```

#### Events
- **Mobile menu open/close**: Announces to screen readers
- **Keyboard navigation**: Arrow keys, Tab, Escape
- **Focus management**: Maintains logical focus flow

#### Example Usage
```javascript
import { Navigation } from './components/Navigation.js';

const navElement = document.querySelector('.header nav');
const navigation = new Navigation(navElement, {
  mobileBreakpoint: 768,
  closeOnOutsideClick: true
});

// Set active page
navigation.setActive(window.location.pathname);
```

### ContactForm

**File**: `src/scripts/components/ContactForm.js`

Handles contact form validation, submission, and user feedback.

#### Constructor
```javascript
const contactForm = new ContactForm(element, options);
```

**Parameters:**
- `element` (HTMLElement): Form element
- `options` (Object): Configuration options

**Options:**
```javascript
{
  validateOnBlur: true,         // Validate fields on blur
  showSuccessMessage: true,     // Show success message after submission
  resetOnSuccess: true,         // Reset form after successful submission
  trackAnalytics: false         // Track form events with analytics
}
```

#### Methods

##### `init()`
Initializes the contact form.
```javascript
contactForm.init();
```

##### `validate()`
Validates the entire form.
```javascript
const isValid = contactForm.validate();
```

##### `validateField(field)`
Validates a specific field.
```javascript
const emailField = document.getElementById('email');
const isValid = contactForm.validateField(emailField);
```

##### `submit()`
Submits the form.
```javascript
contactForm.submit();
```

##### `reset()`
Resets the form to initial state.
```javascript
contactForm.reset();
```

##### `showError(field, message)`
Shows error message for a field.
```javascript
contactForm.showError(emailField, 'Invalid email address');
```

##### `clearError(field)`
Clears error message for a field.
```javascript
contactForm.clearError(emailField);
```

#### Example Usage
```javascript
import { ContactForm } from './components/ContactForm.js';

const formElement = document.getElementById('contactForm');
const contactForm = new ContactForm(formElement, {
  validateOnBlur: true,
  showSuccessMessage: true
});
```

### LanguageSwitcher

**File**: `src/scripts/components/LanguageSwitcher.js`

Handles language switching functionality with i18n integration.

#### Constructor
```javascript
const languageSwitcher = new LanguageSwitcher(element, i18nService);
```

**Parameters:**
- `element` (HTMLElement): Language switcher container
- `i18nService` (I18nService): Internationalization service instance

#### Methods

##### `init()`
Initializes the language switcher.
```javascript
languageSwitcher.init();
```

##### `switchLanguage(language)`
Switches to specified language.
```javascript
languageSwitcher.switchLanguage('en');
```

##### `updateUI()`
Updates the UI to reflect current language.
```javascript
languageSwitcher.updateUI();
```

#### Example Usage
```javascript
import { LanguageSwitcher } from './components/LanguageSwitcher.js';
import { I18nService } from './services/I18nService.js';

const i18nService = new I18nService();
const langSwitcher = document.querySelector('.language-switcher');
const languageSwitcher = new LanguageSwitcher(langSwitcher, i18nService);
```

## 🔧 Services

### I18nService

**File**: `src/scripts/services/I18nService.js`

Handles internationalization, language detection, and content translation.

#### Constructor
```javascript
const i18nService = new I18nService(options);
```

**Options:**
```javascript
{
  defaultLanguage: 'nl',        // Default language
  fallbackLanguage: 'nl',       // Fallback language
  storageKey: 'techsupport_language'  // localStorage key
}
```

#### Methods

##### `init()`
Initializes the i18n service.
```javascript
await i18nService.init();
```

##### `switchLanguage(language)`
Switches to a different language.
```javascript
await i18nService.switchLanguage('en');
```

##### `t(key, params)`
Gets translation for a key.
```javascript
const translation = i18nService.t('nav.services');
const withParams = i18nService.t('welcome.message', { name: 'John' });
```

##### `getCurrentLanguage()`
Gets current language.
```javascript
const currentLang = i18nService.getCurrentLanguage();
```

##### `getAvailableLanguages()`
Gets list of available languages.
```javascript
const languages = i18nService.getAvailableLanguages();
// Returns: ['nl', 'en', 'fr', 'pt']
```

##### `loadLanguage(language)`
Loads translations for a specific language.
```javascript
await i18nService.loadLanguage('fr');
```

##### `applyTranslations()`
Applies translations to the current page.
```javascript
i18nService.applyTranslations();
```

#### Events
- **languagechange**: Fired when language changes
```javascript
document.addEventListener('languagechange', (event) => {
  console.log('Language changed to:', event.detail.language);
});
```

#### Example Usage
```javascript
import { I18nService } from './services/I18nService.js';

const i18nService = new I18nService({
  defaultLanguage: 'nl',
  fallbackLanguage: 'nl'
});

await i18nService.init();

// Get translation
const title = i18nService.t('hero.title');

// Switch language
await i18nService.switchLanguage('en');
```

### ValidationService

**File**: `src/scripts/services/ValidationService.js`

Provides form validation with internationalized error messages.

#### Constructor
```javascript
const validationService = new ValidationService(language);
```

**Parameters:**
- `language` (String): Language for error messages

#### Methods

##### `validateEmail(email)`
Validates email address.
```javascript
const result = validationService.validateEmail('<EMAIL>');
// Returns: { isValid: true, message: '' }
```

##### `validateRequired(value)`
Validates required field.
```javascript
const result = validationService.validateRequired('');
// Returns: { isValid: false, message: 'This field is required' }
```

##### `validateMinLength(value, minLength)`
Validates minimum length.
```javascript
const result = validationService.validateMinLength('abc', 5);
// Returns: { isValid: false, message: 'Minimum 5 characters required' }
```

##### `validateMaxLength(value, maxLength)`
Validates maximum length.
```javascript
const result = validationService.validateMaxLength('text', 10);
// Returns: { isValid: true, message: '' }
```

##### `validatePhone(phone)`
Validates phone number.
```javascript
const result = validationService.validatePhone('+31612345678');
```

#### Example Usage
```javascript
import { ValidationService } from './services/ValidationService.js';

const validationService = new ValidationService('nl');

// Validate email
const emailResult = validationService.validateEmail('<EMAIL>');
if (!emailResult.isValid) {
  console.error(emailResult.message);
}
```

### ContactService

**File**: `src/scripts/services/ContactService.js`

Handles contact form submission and communication with backend services.

#### Methods

##### `submitForm(formData)`
Submits contact form data.
```javascript
const formData = new FormData(formElement);
const result = await ContactService.submitForm(formData);
```

##### `validateSubmission(data)`
Validates form data before submission.
```javascript
const isValid = ContactService.validateSubmission(formData);
```

#### Example Usage
```javascript
import { ContactService } from './services/ContactService.js';

const formData = new FormData(document.getElementById('contactForm'));
try {
  const result = await ContactService.submitForm(formData);
  console.log('Form submitted successfully:', result);
} catch (error) {
  console.error('Submission failed:', error);
}
```

## 🛠️ Utilities

### DOM Utilities

**File**: `src/scripts/utils/dom.js`

Provides DOM manipulation and query utilities.

#### Functions

##### `$(selector)`
Query single element.
```javascript
import { $ } from './utils/dom.js';
const element = $('.my-class');
```

##### `$$(selector)`
Query multiple elements.
```javascript
import { $$ } from './utils/dom.js';
const elements = $$('.nav-link');
```

##### `ready(callback)`
Execute callback when DOM is ready.
```javascript
import { ready } from './utils/dom.js';
ready(() => {
  console.log('DOM is ready');
});
```

##### `announce(message, priority)`
Announce message to screen readers.
```javascript
import { announce } from './utils/dom.js';
announce('Form submitted successfully', 'polite');
```

##### `prefersReducedMotion()`
Check if user prefers reduced motion.
```javascript
import { prefersReducedMotion } from './utils/dom.js';
if (!prefersReducedMotion()) {
  // Add animations
}
```

### Performance Utilities

**File**: `src/scripts/utils/performance.js`

Provides performance monitoring and optimization utilities.

#### Functions

##### `measurePerformance(name, fn)`
Measure function execution time.
```javascript
import { measurePerformance } from './utils/performance.js';

const result = measurePerformance('myFunction', () => {
  // Your code here
});
```

##### `debounce(func, wait)`
Debounce function calls.
```javascript
import { debounce } from './utils/performance.js';

const debouncedResize = debounce(() => {
  console.log('Window resized');
}, 250);

window.addEventListener('resize', debouncedResize);
```

##### `throttle(func, limit)`
Throttle function calls.
```javascript
import { throttle } from './utils/performance.js';

const throttledScroll = throttle(() => {
  console.log('Scrolling');
}, 100);

window.addEventListener('scroll', throttledScroll);
```

## 🚀 Main Application

**File**: `src/scripts/main.js`

Main application class that orchestrates all components and services.

### App Class

#### Constructor
```javascript
const app = new App();
```

#### Methods

##### `init()`
Initializes the entire application.
```javascript
await app.init();
```

##### `getComponent(name)`
Gets component instance.
```javascript
const navigation = app.getComponent('navigation');
```

##### `getService(name)`
Gets service instance.
```javascript
const i18nService = app.getService('i18n');
```

##### `destroy()`
Destroys the application and cleans up resources.
```javascript
app.destroy();
```

#### Global Access
The app instance is available globally for debugging:
```javascript
// In browser console
window.TechSupportApp.getComponent('navigation');
```

## 📝 Usage Examples

### Complete Integration Example
```javascript
// main.js
import { App } from './App.js';

// Initialize application
const app = new App();
await app.init();

// Access components
const navigation = app.getComponent('navigation');
const i18nService = app.getService('i18n');

// Switch language
await i18nService.switchLanguage('en');

// Set active navigation
navigation.setActive('/services.html');
```

### Custom Component Example
```javascript
// MyComponent.js
export class MyComponent {
  constructor(element, options = {}) {
    this.element = element;
    this.options = { ...this.defaultOptions, ...options };
    this.init();
  }

  get defaultOptions() {
    return {
      autoInit: true,
      className: 'my-component'
    };
  }

  init() {
    this.setupElements();
    this.bindEvents();
    this.setupAccessibility();
  }

  setupElements() {
    this.element.classList.add(this.options.className);
  }

  bindEvents() {
    this.element.addEventListener('click', this.handleClick.bind(this));
  }

  setupAccessibility() {
    this.element.setAttribute('role', 'button');
    this.element.setAttribute('tabindex', '0');
  }

  handleClick(event) {
    // Handle click
  }

  destroy() {
    this.element.removeEventListener('click', this.handleClick);
  }
}
```

---

**📖 This API documentation is maintained alongside the codebase. Please update when making changes to components or services.**
