# TechSupport Pro - Development Setup Guide

Complete guide for setting up the TechSupport Pro website development environment. This guide covers everything from initial setup to advanced development workflows.

## 📋 Prerequisites

### **Required Software**
- **Node.js**: Version 18.0.0 or higher ([Download](https://nodejs.org/))
- **npm**: Version 9.0.0 or higher (comes with Node.js)
- **Git**: Latest version ([Download](https://git-scm.com/))
- **Modern Web Browser**: Chrome 90+, Firefox 88+, Safari 14+, or Edge 90+

### **Recommended Tools**
- **VS Code**: With extensions for better development experience
  - ESLint extension
  - Prettier extension
  - SCSS IntelliSense
  - Auto Rename Tag
  - Live Server (for testing)
- **Terminal**: Command line interface (built-in or external)

### **System Requirements**
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space for dependencies and build files
- **OS**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)

## 🚀 Quick Start

### **1. <PERSON>lone and Install**
```bash
# Clone the repository
git clone <repository-url>
cd techsupport-pro

# Install dependencies (this may take a few minutes)
npm install

# Verify installation
npm run --version
```

### **2. Start Development Server**
```bash
# Start development server with hot module replacement
npm run dev

# Server will start at http://localhost:3000
# The browser should open automatically
```

### **3. Verify Setup**
- Open `http://localhost:3000` in your browser
- You should see the TechSupport Pro homepage
- Try changing a file in `src/` - the page should auto-reload
- Check browser console for any errors

### **4. Build for Production**
```bash
# Create optimized production build
npm run build

# Preview the production build
npm run preview
# Opens at http://localhost:4173
```

## 📜 Available Scripts

### **🔧 Development Scripts**
```bash
# Start development server with hot module replacement
npm run dev
# → Starts server at http://localhost:3000
# → Automatically opens browser
# → Watches for file changes and reloads

# Build optimized production bundle
npm run build
# → Creates dist/ directory with optimized files
# → Minifies CSS, JS, and images
# → Generates source maps for debugging

# Preview production build locally
npm run preview
# → Serves the dist/ directory at http://localhost:4173
# → Test production build before deployment

# Clean build artifacts
npm run clean
# → Removes dist/ directory
# → Useful for fresh builds

# Build and serve (combined command)
npm run serve
# → Runs build then preview
```

### **✅ Code Quality Scripts**
```bash
# Check JavaScript code for errors and style issues
npm run lint
# → Uses ESLint to check all JS files in src/
# → Reports errors and warnings

# Automatically fix JavaScript issues
npm run lint:fix
# → Fixes auto-fixable ESLint issues
# → Manual fixes still required for some issues

# Format code with Prettier
npm run format
# → Formats all JS, CSS, HTML, SCSS files
# → Ensures consistent code style

# Check if code is properly formatted
npm run format:check
# → Verifies formatting without making changes
# → Useful for CI/CD pipelines
```

### **🧪 Testing Scripts**
```bash
# Run all unit tests
npm test
# → Runs Jest test suite
# → Shows test results and coverage

# Run tests in watch mode (for development)
npm run test:watch
# → Watches for file changes and re-runs tests
# → Interactive mode for test development

# Run accessibility tests
npm run a11y-test
# → Uses Pa11y to test WCAG compliance
# → Tests all pages defined in sitemap

# Run Lighthouse performance audit
npm run lighthouse
# → Generates performance report
# → Saves HTML report to reports/ directory
```

### **⚡ Optimization Scripts**
```bash
# Optimize images for web
npm run optimize-images
# → Converts images to WebP and AVIF formats
# → Compresses images for better performance
# → Maintains original files as backup

# Generate XML sitemap for SEO
npm run generate-sitemap
# → Creates sitemap.xml in public/ directory
# → Automatically discovers all HTML pages
# → Updates lastmod dates
```

## 📁 Project Structure Overview

```
techsupport-pro/
├── src/                          # 🎯 Source files (development)
│   ├── assets/                   # 🖼️ Static assets
│   │   ├── images/              # Images (original and optimized)
│   │   ├── icons/               # SVG icons and favicons
│   │   └── fonts/               # Local font files
│   ├── styles/                   # 🎨 SCSS stylesheets (7-1 architecture)
│   │   ├── abstracts/           # Variables, mixins, functions
│   │   ├── base/                # Reset, typography, base styles
│   │   ├── components/          # Reusable UI components
│   │   ├── layout/              # Header, footer, navigation, grid
│   │   ├── pages/               # Page-specific styles
│   │   ├── themes/              # Theme variations (light/dark)
│   │   ├── utilities/           # Helper classes
│   │   └── main.scss            # Main stylesheet entry point
│   ├── scripts/                  # ⚡ JavaScript modules (ES6+)
│   │   ├── components/          # UI component classes
│   │   ├── services/            # Business logic and data services
│   │   ├── utils/               # Utility functions and helpers
│   │   ├── config/              # Configuration files
│   │   └── main.js              # Application entry point
│   ├── templates/                # 📄 HTML templates and components
│   ├── data/                     # 📊 Static data files
│   │   └── i18n/                # Translation files (nl, en, fr, pt)
│   ├── content/                  # 📝 Content management
│   └── index.html               # Main HTML entry point
├── public/                       # 🌐 Static files (copied to dist)
├── dist/                         # 📦 Production build (generated)
├── tests/                        # 🧪 Test files
├── docs/                         # 📚 Documentation
├── tools/                        # 🔧 Build tools and scripts
└── reports/                      # 📊 Generated reports
```

For detailed structure explanation, see [RECOMMENDED_STRUCTURE.md](../RECOMMENDED_STRUCTURE.md).

## 🔄 Development Workflow

### **1. 🌟 Feature Development**
```bash
# Create and switch to feature branch
git checkout -b feature/your-feature-name

# Start development server
npm run dev

# Make changes in src/ directory
# - Edit files in src/scripts/ for JavaScript
# - Edit files in src/styles/ for CSS/SCSS
# - Edit src/index.html or create new pages

# Test changes in browser (auto-reloads)
# Check browser console for errors

# Run quality checks before committing
npm run lint && npm run format:check
```

### **2. 🧪 Testing Workflow**
```bash
# Write unit tests in tests/ directory
# - Mirror src/ structure in tests/
# - Use .test.js suffix for test files

# Run tests during development
npm run test:watch

# Run full test suite
npm test

# Check accessibility compliance
npm run a11y-test

# Performance testing
npm run lighthouse
```

### **3. ⚡ Performance Optimization**
```bash
# Optimize images before committing
npm run optimize-images

# Check performance metrics
npm run lighthouse

# Monitor Core Web Vitals in browser DevTools
# - Performance tab
# - Lighthouse panel
# - Network tab for loading analysis
```

### **4. 🚀 Pre-Deployment Checklist**
```bash
# 1. Code quality
npm run lint:fix
npm run format

# 2. Testing
npm test
npm run a11y-test

# 3. Build and test
npm run build
npm run preview

# 4. Performance check
npm run lighthouse

# 5. Generate sitemap
npm run generate-sitemap

# 6. Commit and push
git add .
git commit -m "feat: your feature description"
git push origin feature/your-feature-name
```

## ⚙️ Configuration Files

### **🏗️ Build Configuration**

#### **Vite (`vite.config.js`)**
- **Purpose**: Main build tool configuration
- **Features**:
  - SCSS compilation with auto-imports
  - JavaScript bundling and code splitting
  - Asset optimization and hashing
  - Development server with HMR
  - Production optimization
- **Key Settings**:
  - Root: `src/` directory
  - Output: `dist/` directory
  - Aliases for clean imports (`@`, `@components`, etc.)

#### **PostCSS (`postcss.config.js`)**
- **Purpose**: CSS processing pipeline
- **Plugins**:
  - Autoprefixer: Browser compatibility
  - CSSnano: CSS minification and optimization
- **Browser Support**: Defined in `package.json` browserslist

### **📏 Code Quality Configuration**

#### **ESLint (`.eslintrc.js`)**
- **Purpose**: JavaScript code quality and consistency
- **Rules**:
  - ES6+ syntax enforcement
  - Best practices for modern JavaScript
  - Accessibility rules (jsx-a11y)
  - Import/export validation
- **Integration**: VS Code extension available

#### **Prettier (`.prettierrc`)**
- **Purpose**: Automatic code formatting
- **Scope**: JavaScript, CSS, HTML, SCSS, JSON
- **Settings**:
  - 2-space indentation
  - Single quotes for strings
  - Trailing commas
  - Line length: 100 characters

### **🧪 Testing Configuration**

#### **Jest (`jest.config.js`)**
- **Environment**: jsdom (browser simulation)
- **Test Files**: `*.test.js` in `tests/` and `src/`
- **Coverage**: Tracks coverage for all `src/scripts/` files
- **Setup**: Custom setup file for test utilities

#### **Babel (`babel.config.js`)**
- **Purpose**: JavaScript transpilation for tests
- **Presets**: Current Node.js syntax support
- **Integration**: Works with Jest for ES6+ module testing

### **📊 Quality Assurance Configuration**

#### **Lighthouse (`lighthouserc.js`)**
- **URLs**: Tests homepage, services, and contact pages
- **Assertions**: Performance, accessibility, SEO thresholds
- **Reports**: Generates HTML reports in `reports/` directory

#### **Pa11y (Accessibility)**
- **Integration**: Via npm script
- **Standards**: WCAG 2.1 AA compliance
- **Scope**: All pages in sitemap

## 🎯 Browser Support & Targets

### **Supported Browsers**
- **Chrome**: 90+ (Chromium-based browsers)
- **Firefox**: 88+ (Gecko engine)
- **Safari**: 14+ (WebKit engine)
- **Edge**: 90+ (Chromium-based)

### **Progressive Enhancement**
- Core functionality works without JavaScript
- CSS Grid with Flexbox fallbacks
- Modern features with graceful degradation

### **Performance Targets**
- **Lighthouse Performance**: 95+ (excellent)
- **Lighthouse Accessibility**: 100 (perfect)
- **Lighthouse Best Practices**: 95+ (excellent)
- **Lighthouse SEO**: 100 (perfect)
- **Core Web Vitals**: All metrics in green zone

## 🔧 Troubleshooting Guide

### **🚨 Common Issues & Solutions**

#### **1. Build Errors**
```bash
# Clear Vite cache
rm -rf node_modules/.vite

# Clear all caches and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for Node.js version compatibility
node --version  # Should be 18.0.0+
npm --version   # Should be 9.0.0+
```

#### **2. SCSS Compilation Issues**
```bash
# Check main.scss imports
# Ensure all @use statements are at the top
# Verify partial files start with underscore (_)

# Common fixes:
# - Check file paths in src/styles/main.scss
# - Ensure SCSS syntax is valid
# - Verify variable names match in _variables.scss
```

#### **3. JavaScript Module Errors**
```bash
# Check import/export syntax
# ES6 modules: import/export (not require/module.exports)
# Verify file extensions (.js)
# Check file paths (case-sensitive)

# Example correct syntax:
# import { Component } from './Component.js';
# export default MyClass;
```

#### **4. Development Server Issues**
```bash
# Port already in use
npm run dev -- --port 3001

# Clear browser cache
# Hard refresh: Ctrl+Shift+R (Windows/Linux) or Cmd+Shift+R (Mac)

# Check firewall/antivirus blocking localhost
# Temporarily disable and test
```

#### **5. Image Optimization Errors**
```bash
# Install sharp dependencies (if missing)
npm install sharp --save-dev

# Check image file formats
# Supported: PNG, JPG, JPEG, WebP, AVIF, SVG

# Verify image file permissions
# Ensure src/assets/images/ is writable
```

### **🔍 Debugging Techniques**

#### **Browser Developer Tools**
```bash
# Console Tab
# - Check for JavaScript errors
# - Look for failed network requests
# - Monitor console.log outputs

# Network Tab
# - Check loading times
# - Verify all assets load correctly
# - Monitor failed requests (404s, 500s)

# Performance Tab
# - Analyze loading performance
# - Check Core Web Vitals
# - Identify performance bottlenecks

# Lighthouse Tab
# - Run performance audits
# - Check accessibility issues
# - Get optimization suggestions
```

#### **Command Line Debugging**
```bash
# Verbose npm output
npm run dev --verbose

# Check specific script issues
npm run lint -- --debug
npm run build -- --debug

# Test individual components
npm test -- --verbose ComponentName
```

### **📞 Getting Help**

#### **Self-Diagnosis Checklist**
- [ ] Check browser console for errors
- [ ] Run `npm run lint` for code quality issues
- [ ] Verify all dependencies are installed (`npm install`)
- [ ] Check Node.js and npm versions
- [ ] Clear browser cache and hard refresh
- [ ] Test in different browser
- [ ] Check network connectivity

#### **Performance Issues**
```bash
# Run performance audit
npm run lighthouse

# Check bundle size
npm run build
# Check dist/ directory sizes

# Analyze bundle composition
# Use browser DevTools > Network tab
# Look for large files or slow loading
```

#### **Accessibility Issues**
```bash
# Run accessibility tests
npm run a11y-test

# Manual testing checklist:
# - Tab navigation works
# - Screen reader compatibility
# - Color contrast sufficient
# - Images have alt text
# - Forms have labels
```

#### **When to Seek Help**
1. **Error persists** after following troubleshooting steps
2. **Performance targets** not met despite optimization
3. **Accessibility issues** that can't be resolved
4. **Build process** fails consistently
5. **New feature implementation** needs architectural guidance

#### **Resources**
- [Vite Documentation](https://vitejs.dev/)
- [SCSS Documentation](https://sass-lang.com/)
- [Jest Testing Framework](https://jestjs.io/)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Core Web Vitals](https://web.dev/vitals/)

---

**💡 Tip**: Keep this setup guide bookmarked for quick reference during development!
