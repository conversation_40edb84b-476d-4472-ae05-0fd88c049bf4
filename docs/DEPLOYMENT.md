# TechSupport Pro - Deployment Guide

Complete guide for deploying the TechSupport Pro website to production environments. This covers build optimization, deployment strategies, and production configuration.

## 🚀 Quick Deployment

### **Production Build**
```bash
# Install dependencies
npm install

# Run quality checks
npm run lint && npm run format:check && npm test

# Optimize assets
npm run optimize-images
npm run generate-sitemap

# Create production build
npm run build

# Test production build locally
npm run preview
```

The `dist/` directory contains all files needed for deployment.

## 📦 Build Process

### **Build Steps**
1. **Code Quality**: ESLint and Prettier validation
2. **Testing**: Unit tests and accessibility checks
3. **Asset Optimization**: Image compression, CSS/JS minification
4. **Bundling**: Code splitting and tree shaking
5. **Hashing**: Asset fingerprinting for cache busting
6. **Service Worker**: Offline functionality generation

### **Build Output Structure**
```
dist/
├── assets/
│   ├── css/
│   │   └── main-[hash].css      # Minified CSS
│   ├── js/
│   │   ├── main-[hash].js       # Main application bundle
│   │   ├── vendor-[hash].js     # Third-party dependencies
│   │   └── components-[hash].js # Component chunks
│   ├── images/
│   │   ├── *.webp              # Optimized WebP images
│   │   ├── *.avif              # Modern AVIF images
│   │   └── *.png               # Fallback images
│   └── fonts/
│       └── *.woff2             # Optimized font files
├── index.html                   # Main HTML file
├── robots.txt                   # SEO directives
├── sitemap.xml                  # SEO sitemap
├── site.webmanifest            # PWA manifest
└── sw.js                       # Service Worker
```

## 🌐 Deployment Platforms

### **Static Hosting (Recommended)**

#### **Netlify**
```bash
# Build command
npm run build

# Publish directory
dist

# Environment variables (if needed)
NODE_ENV=production
```

**Netlify Configuration (`netlify.toml`)**:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### **Vercel**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Or use GitHub integration
# Push to main branch for automatic deployment
```

**Vercel Configuration (`vercel.json`)**:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": null,
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

#### **GitHub Pages**
```bash
# Install gh-pages
npm install --save-dev gh-pages

# Add to package.json scripts
"deploy": "gh-pages -d dist"

# Deploy
npm run build
npm run deploy
```

### **Traditional Hosting**

#### **Apache Server**
Upload `dist/` contents to web root. Add `.htaccess`:
```apache
# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Security headers
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# SPA routing
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

#### **Nginx Server**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/techsupport-pro;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Cache static assets
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## ⚙️ Environment Configuration

### **Environment Variables**
```bash
# Production environment
NODE_ENV=production

# API endpoints (if needed)
VITE_API_URL=https://api.techsupportpro.nl

# Analytics (if needed)
VITE_GA_ID=GA_MEASUREMENT_ID

# Feature flags
VITE_ENABLE_PWA=true
VITE_ENABLE_ANALYTICS=true
```

### **Build Optimization**

#### **Performance Optimizations**
- **Code Splitting**: Automatic vendor and component chunks
- **Tree Shaking**: Removes unused code
- **Minification**: CSS and JavaScript compression
- **Image Optimization**: WebP/AVIF conversion with fallbacks
- **Font Optimization**: WOFF2 format with preloading
- **Service Worker**: Caching strategy for offline functionality

#### **SEO Optimizations**
- **Meta Tags**: Complete Open Graph and Twitter Card support
- **Structured Data**: Schema.org markup ready
- **Sitemap**: Automatically generated XML sitemap
- **Robots.txt**: Search engine directives
- **Performance**: Fast loading for better rankings

## 🔒 Security Considerations

### **Content Security Policy**
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self';
">
```

### **Security Headers**
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing
- **X-XSS-Protection**: XSS attack protection
- **Referrer-Policy**: Controls referrer information
- **HTTPS**: Always use HTTPS in production

## 📊 Monitoring & Analytics

### **Performance Monitoring**
```bash
# Lighthouse CI for continuous monitoring
npm install -g @lhci/cli

# Run Lighthouse CI
lhci autorun
```

### **Error Tracking**
Consider integrating:
- **Sentry**: Error tracking and performance monitoring
- **LogRocket**: Session replay and debugging
- **Google Analytics**: User behavior tracking

## ✅ Pre-Deployment Checklist

### **Code Quality**
- [ ] All tests passing (`npm test`)
- [ ] No linting errors (`npm run lint`)
- [ ] Code properly formatted (`npm run format:check`)
- [ ] Accessibility tests pass (`npm run a11y-test`)

### **Performance**
- [ ] Lighthouse scores meet targets (`npm run lighthouse`)
- [ ] Images optimized (`npm run optimize-images`)
- [ ] Bundle size acceptable (check `dist/` directory)
- [ ] Core Web Vitals in green zone

### **SEO & Content**
- [ ] Sitemap generated (`npm run generate-sitemap`)
- [ ] Meta tags updated for production
- [ ] Content reviewed and approved
- [ ] All links working correctly

### **Security**
- [ ] Security headers configured
- [ ] HTTPS certificate installed
- [ ] Environment variables secured
- [ ] No sensitive data in client code

### **Functionality**
- [ ] All features working in production build
- [ ] Forms submitting correctly
- [ ] Language switching functional
- [ ] Mobile navigation working
- [ ] Service Worker caching properly

## 🔄 Continuous Deployment

### **GitHub Actions Example**
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Run linting
      run: npm run lint
    
    - name: Build
      run: npm run build
    
    - name: Deploy to Netlify
      uses: nwtgck/actions-netlify@v1.2
      with:
        publish-dir: './dist'
        production-branch: main
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
```

---

**🎉 Your TechSupport Pro website is now ready for production deployment!**
