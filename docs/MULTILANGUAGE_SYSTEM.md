# TechSupport Pro - Multilanguage System Documentation

Complete guide for the internationalization (i18n) system implemented in the TechSupport Pro website. This system provides seamless multilanguage support with accessibility and performance optimization.

## 🌐 Overview

The TechSupport Pro website features a comprehensive multilanguage system supporting **4 languages**:
- **Dutch (nl)** - Primary language and default
- **English (en)** - International audience
- **French (fr)** - European market
- **Portuguese (pt)** - Additional market support

### **Key Capabilities**
- **Automatic Language Detection**: Browser, URL, and localStorage detection
- **Real-time Switching**: No page reload required
- **Persistent Preferences**: User choices saved across sessions
- **Accessibility First**: Full screen reader and keyboard support
- **Performance Optimized**: Lazy loading and caching strategies
- **SEO Friendly**: Proper language attributes and meta tags

## ✨ Features

### **🔍 Language Detection System**

#### **Detection Priority (Highest to Lowest)**
1. **URL Parameter**: `?lang=en` (immediate override)
2. **localStorage**: Previously selected language preference
3. **Browser Language**: `navigator.language` detection
4. **Default Fallback**: Dutch (nl) as primary language

#### **Smart Detection Logic**
```javascript
// Implemented in I18nService.js
detectLanguage() {
  // 1. Check URL parameter
  const urlParams = new URLSearchParams(window.location.search);
  const urlLang = urlParams.get('lang');
  if (urlLang && this.isValidLanguage(urlLang)) {
    return urlLang;
  }

  // 2. Check localStorage
  const storedLang = localStorage.getItem(this.options.storageKey);
  if (storedLang && this.isValidLanguage(storedLang)) {
    return storedLang;
  }

  // 3. Check browser language
  const browserLang = navigator.language.split('-')[0];
  if (this.isValidLanguage(browserLang)) {
    return browserLang;
  }

  // 4. Fall back to default
  return this.options.defaultLanguage;
}
```

### **📝 Translation Management**

#### **JSON-Based Translation Files**
- **Structured Data**: Hierarchical organization for easy maintenance
- **Nested Keys**: Support for complex content structures
- **Parameter Interpolation**: Dynamic content with `{{variable}}` syntax
- **Fallback System**: Graceful handling of missing translations

#### **Translation File Structure**
```json
{
  "nav": {
    "home": "Start",
    "services": "Diensten",
    "about": "Over Ons"
  },
  "hero": {
    "title": "IT Ondersteuning {{type}}",
    "subtitle": "Directe hulp bij computer problemen"
  },
  "services": {
    "software": {
      "title": "Software & Digitale Diensten",
      "service1": "Bestandsherstel",
      "service1_desc": "Terughalen van verloren bestanden"
    }
  }
}
```

### **🎛️ User Interface Components**

#### **Language Switcher Dropdown**
- **Visual Flags**: Country flag icons for easy identification
- **Keyboard Navigation**: Full keyboard accessibility
- **Active State**: Clear indication of current language
- **Loading States**: Visual feedback during language switching
- **Mobile Optimized**: Touch-friendly interface

#### **Accessibility Features**
- **ARIA Labels**: Proper labeling for screen readers
- **Role Attributes**: Semantic markup for assistive technologies
- **Focus Management**: Logical tab order and focus indicators
- **Screen Reader Announcements**: Language change notifications

### **📄 Content Coverage**

#### **Fully Translated Sections**
- ✅ **Navigation Menu**: All menu items and dropdowns
- ✅ **Hero Section**: Headlines, subtitles, and call-to-action buttons
- ✅ **Services**: Complete service descriptions and features
- ✅ **FAQ Section**: Questions, answers, and interactions
- ✅ **Footer**: Contact information and links
- ✅ **Forms**: Labels, placeholders, validation messages, and success/error states
- ✅ **Error Pages**: 404 and other error page content
- ✅ **Meta Tags**: Page titles and descriptions for SEO

## 📁 File Structure & Organization

```
techsupport-pro/
├── src/
│   ├── data/i18n/                    # 🌐 Translation files
│   │   ├── nl.json                   # Dutch (primary language)
│   │   ├── en.json                   # English translations
│   │   ├── fr.json                   # French translations
│   │   ├── pt.json                   # Portuguese translations
│   │   ├── nl.js                     # JavaScript format (alternative)
│   │   ├── en.js                     # JavaScript format (alternative)
│   │   ├── fr.js                     # JavaScript format (alternative)
│   │   └── pt.js                     # JavaScript format (alternative)
│   ├── scripts/
│   │   ├── services/
│   │   │   └── I18nService.js        # 🔧 Core i18n functionality
│   │   └── components/
│   │       └── LanguageSwitcher.js   # 🎛️ Language switcher UI component
│   └── assets/images/flags/          # 🏳️ Flag icons
│       ├── nl.svg                    # Dutch flag
│       ├── en.svg                    # English flag
│       ├── fr.svg                    # French flag
│       └── pt.svg                    # Portuguese flag
└── docs/
    └── MULTILANGUAGE_SYSTEM.md       # This documentation
```

## 🚀 Implementation Guide

### **HTML Markup Integration**

#### **Basic Translation Attributes**
```html
<!-- Simple text translation -->
<h1 data-i18n="hero.title">IT Ondersteuning op Menselijke Maat</h1>
<p data-i18n="hero.subtitle">Directe hulp bij computer problemen</p>
<button data-i18n="hero.cta">Vraag Grátis Advies Aan</button>

<!-- Navigation items -->
<nav>
  <a href="/diensten.html" data-i18n="nav.services">Diensten</a>
  <a href="/over-ons.html" data-i18n="nav.about">Over Ons</a>
  <a href="/contact.html" data-i18n="nav.contact">Contact</a>
</nav>
```

#### **Attribute Translation**
```html
<!-- Translate placeholder text -->
<input
  type="email"
  data-i18n="contact.form.email"
  data-i18n-attr="placeholder"
  placeholder="E-mailadres">

<!-- Translate alt text -->
<img
  src="logo.svg"
  data-i18n="logo.alt"
  data-i18n-attr="alt"
  alt="TechSupport Pro Logo">

<!-- Translate aria-label -->
<button
  data-i18n="nav.menu_toggle"
  data-i18n-attr="aria-label"
  aria-label="Menu openen">
  <span class="hamburger"></span>
</button>
```

#### **Complex Content with Parameters**
```html
<!-- Content with dynamic values -->
<p data-i18n="welcome.message" data-i18n-params='{"name": "John", "company": "TechSupport Pro"}'>
  Welcome {{name}} to {{company}}
</p>

<!-- Form validation messages -->
<div id="email-error" data-i18n="validation.email.invalid" role="alert">
  Please enter a valid email address
</div>
```

### **JavaScript API Usage**

#### **Service Initialization**
```javascript
import { I18nService } from './services/I18nService.js';

// Initialize with options
const i18nService = new I18nService({
  defaultLanguage: 'nl',
  fallbackLanguage: 'nl',
  storageKey: 'techsupport_language'
});

// Initialize the service
await i18nService.init();
```

#### **Getting Translations**
```javascript
// Simple translation
const homeText = i18nService.t('nav.home');
// Returns: "Start" (in Dutch) or "Home" (in English)

// Translation with parameters
const welcomeMessage = i18nService.t('welcome.message', {
  name: 'John',
  company: 'TechSupport Pro'
});
// Returns: "Welkom John bij TechSupport Pro"

// Nested translation keys
const serviceTitle = i18nService.t('services.software.title');
// Returns: "Software & Digitale Diensten"

// Fallback for missing keys
const missingKey = i18nService.t('non.existent.key');
// Returns: "non.existent.key" (the key itself as fallback)
```

#### **Language Management**
```javascript
// Get current language
const currentLang = i18nService.getCurrentLanguage();
// Returns: "nl", "en", "fr", or "pt"

// Get available languages
const languages = i18nService.getAvailableLanguages();
// Returns: ["nl", "en", "fr", "pt"]

// Switch language
await i18nService.switchLanguage('en');
// Loads English translations and updates the page

// Check if language is supported
const isSupported = i18nService.isValidLanguage('de');
// Returns: false (German not supported)

// Preload all languages for better performance
await i18nService.preloadLanguages();
```

#### **Event Handling**
```javascript
// Listen for language changes
document.addEventListener('languagechange', (event) => {
  console.log('Language changed from', event.detail.previousLanguage);
  console.log('Language changed to', event.detail.language);

  // Update other components
  updateFormValidation(event.detail.language);
  trackLanguageChange(event.detail.language);
});

// Manual translation application
i18nService.applyTranslations();
```

### **Translation File Structure**

#### **Complete Translation Schema (English Example)**
```json
{
  "nav": {
    "home": "Home",
    "services": "Services",
    "about": "About Us",
    "testimonials": "Testimonials",
    "blog": "Blog",
    "contact": "Contact"
  },
  "hero": {
    "title": "IT Support on a Human Scale",
    "subtitle": "Direct help with computer and network problems without technical jargon.",
    "cta": "Request Free Advice",
    "secondary": "Our Services"
  },
  "services": {
    "title": "Our Professional Services",
    "software": {
      "title": "Software & Digital Services",
      "description": "Problem solving and optimization of your digital workspace:",
      "service1": "File Recovery",
      "service1_desc": "Retrieving lost photos or documents",
      "service2": "Virus Solutions",
      "service2_desc": "Cleaning hacked or slow systems",
      "service3": "Software Installation",
      "service3_desc": "Office, accounting software and special tools",
      "service4": "System Optimization",
      "service4_desc": "Speeding up computers and optimal performance",
      "cta": "More Info"
    },
    "support": {
      "title": "Fast & Secure Online Support",
      "description": "Immediate remote assistance - secure and connected within 15 minutes:",
      "service1": "Live screen help",
      "service1_desc": "Direct solution while you watch",
      "service2": "Email problems",
      "service2_desc": "Configuration and troubleshooting",
      "service3": "Backup guidance",
      "service3_desc": "Secure storage of important files",
      "service4": "Remote work support",
      "service4_desc": "VPN, Teams/Zoom installation",
      "cta": "More Info"
    }
  },
  "faq": {
    "title": "Frequently Asked Questions",
    "q1": "How quickly can you help?",
    "a1": "For online support we are usually available within 15-30 minutes. For more complex problems we schedule an appointment within 24 hours.",
    "q2": "What does your service cost?",
    "a2": "We offer transparent pricing from €45 per hour. The first consultation is always free, so you know exactly what to expect.",
    "q3": "Is online help secure?",
    "a3": "Yes, we use secure connections and you always maintain control. You can end the session at any time and we never ask for passwords."
  },
  "contact": {
    "title": "Contact",
    "form": {
      "name": "Your name",
      "email": "Email address",
      "subject": "Subject",
      "message": "Message",
      "send": "Send message",
      "success": "Thank you for your message! We will contact you within 24 hours."
    }
  },
  "footer": {
    "about": "TechSupport Pro",
    "about_text": "Professional IT support for businesses and individuals throughout the Netherlands.",
    "copyright": "All rights reserved."
  }
}
```

## 🔄 Language Switching System

### **Automatic Detection Flow**

#### **Detection Priority (Highest to Lowest)**
1. **URL Parameter**: `?lang=en` - Immediate override for sharing/bookmarking
2. **localStorage**: `techsupport_language` - User's previous choice
3. **Browser Language**: `navigator.language` - System preference
4. **Default Fallback**: Dutch (nl) - Primary market language

#### **Detection Implementation**
```javascript
// Smart detection with validation
detectLanguage() {
  const supportedLanguages = ['nl', 'en', 'fr', 'pt'];

  // 1. URL parameter (highest priority)
  const urlLang = new URLSearchParams(window.location.search).get('lang');
  if (urlLang && supportedLanguages.includes(urlLang)) {
    return urlLang;
  }

  // 2. Stored preference
  const storedLang = localStorage.getItem('techsupport_language');
  if (storedLang && supportedLanguages.includes(storedLang)) {
    return storedLang;
  }

  // 3. Browser language (extract primary language code)
  const browserLang = navigator.language.split('-')[0];
  if (supportedLanguages.includes(browserLang)) {
    return browserLang;
  }

  // 4. Default fallback
  return 'nl';
}
```

### **Manual Language Switching**

#### **User Interface Methods**
- **Dropdown Menu**: Flag-based language selector in navigation
- **URL Parameters**: Direct links with `?lang=code` parameter
- **Keyboard Shortcuts**: Accessible navigation through language options
- **JavaScript API**: Programmatic language switching

#### **Switching Process**
1. **Validation**: Check if target language is supported
2. **Loading**: Fetch translation file if not already loaded
3. **Application**: Update all page content with new translations
4. **Persistence**: Save choice to localStorage
5. **URL Update**: Update URL parameter without page reload
6. **UI Update**: Update language switcher visual state
7. **Event Notification**: Trigger `languagechange` event

### **Persistence & State Management**

#### **localStorage Integration**
```javascript
// Save language preference
saveLanguagePreference() {
  localStorage.setItem('techsupport_language', this.currentLanguage);
}

// Retrieve saved preference
getSavedLanguage() {
  return localStorage.getItem('techsupport_language');
}
```

#### **URL State Management**
```javascript
// Update URL without page reload
updateURL() {
  const url = new URL(window.location);
  url.searchParams.set('lang', this.currentLanguage);
  window.history.replaceState({}, '', url.toString());
}
```

#### **Session Persistence**
- Language choice persists across browser sessions
- URL parameters allow direct language linking
- Page refreshes maintain selected language
- Cross-tab synchronization (same domain)

## ♿ Accessibility Features

### **Screen Reader Support**

#### **ARIA Implementation**
```html
<!-- Language switcher with proper ARIA -->
<div class="language-switcher" role="none">
  <button
    class="nav-link dropdown-toggle"
    id="language-dropdown-toggle"
    aria-expanded="false"
    aria-haspopup="true"
    aria-controls="language-menu"
    role="menuitem">
    <img src="/flags/nl.svg" alt="NL flag" aria-hidden="true" class="flag-icon">
    <span class="current-language">Nederlands</span>
    <i class="fas fa-chevron-down" aria-hidden="true"></i>
  </button>

  <ul id="language-menu"
      class="nav-dropdown-menu"
      role="menu"
      aria-labelledby="language-dropdown-toggle">
    <li class="nav-dropdown-item" role="none">
      <a href="#" role="menuitem" class="nav-link active" aria-current="true" data-lang="nl">
        <img src="/flags/nl.svg" alt="" aria-hidden="true" class="flag-icon">
        <span class="language-text">Nederlands</span>
      </a>
    </li>
  </ul>
</div>
```

#### **Screen Reader Announcements**
```javascript
// Announce language changes
announceLanguageChange(newLanguage, previousLanguage) {
  const languageNames = {
    nl: 'Nederlands',
    en: 'English',
    fr: 'Français',
    pt: 'Português'
  };

  const message = `Language changed from ${languageNames[previousLanguage]} to ${languageNames[newLanguage]}`;
  this.announce(message, 'polite');
}
```

### **Keyboard Navigation**

#### **Navigation Support**
- **Tab Navigation**: Move through language options
- **Enter/Space**: Select language option
- **Escape**: Close language dropdown
- **Arrow Keys**: Navigate within dropdown menu
- **Home/End**: Jump to first/last option

#### **Focus Management**
```javascript
// Maintain focus during language switching
switchLanguage(language) {
  const currentFocus = document.activeElement;

  // Switch language
  await this.i18nService.switchLanguage(language);

  // Restore focus to appropriate element
  if (currentFocus && currentFocus.isConnected) {
    currentFocus.focus();
  }
}
```

### **Visual Accessibility**

#### **High Contrast Support**
- Flag icons with sufficient contrast
- Clear active state indicators
- Visible focus indicators for keyboard users
- Loading states with visual feedback

#### **Responsive Design**
- Touch-friendly targets (44px minimum)
- Scalable text and icons
- Works at 200% zoom level
- Mobile-optimized interface

## 🌐 Browser Support & Compatibility

### **Modern Browser Support**
- **Chrome 90+**: Full ES6+ module support
- **Firefox 88+**: Complete feature compatibility
- **Safari 14+**: WebKit engine support
- **Edge 90+**: Chromium-based compatibility

### **Progressive Enhancement**
```javascript
// Graceful degradation for older browsers
if (!window.fetch) {
  // Fallback to XMLHttpRequest
  console.warn('Using XMLHttpRequest fallback for older browsers');
}

if (!window.localStorage) {
  // Fallback to session storage or cookies
  console.warn('localStorage not available, using session storage');
}
```

### **No-JavaScript Fallback**
- Static content remains accessible
- Default language (Dutch) always available
- Basic navigation still functional
- Forms work with server-side processing

## ⚡ Performance Optimizations

### **Lazy Loading Strategy**

#### **On-Demand Translation Loading**
```javascript
// Load translations only when needed
async loadLanguage(language) {
  // Check cache first
  if (this.translations.has(language)) {
    return this.translations.get(language);
  }

  // Load from server
  const translations = await this.fetchTranslations(language);

  // Cache for future use
  this.translations.set(language, translations);

  return translations;
}
```

#### **Preloading Critical Languages**
```javascript
// Preload commonly used languages
async preloadCriticalLanguages() {
  const criticalLanguages = ['nl', 'en']; // Most common
  const promises = criticalLanguages.map(lang => this.loadLanguage(lang));
  await Promise.all(promises);
}
```

### **Efficient DOM Updates**

#### **Batch Processing**
```javascript
// Update all translations in a single batch
applyTranslations() {
  const elements = document.querySelectorAll('[data-i18n]');

  // Use DocumentFragment for batch updates
  const fragment = document.createDocumentFragment();

  elements.forEach(element => {
    const key = element.getAttribute('data-i18n');
    const translation = this.t(key);

    // Update text content
    element.textContent = translation;
  });

  // Trigger single reflow
  requestAnimationFrame(() => {
    // DOM updates complete
  });
}
```

#### **Minimal Reflow/Repaint**
- Update only changed elements
- Use CSS transforms for animations
- Avoid layout-triggering properties
- Batch DOM modifications

### **Caching Strategy**

#### **Memory Caching**
```javascript
// In-memory translation cache
this.translations = new Map();

// Cache loaded translations
this.translations.set(language, translationData);
```

#### **localStorage Caching**
```javascript
// Cache translations in localStorage for faster loading
cacheTranslations(language, translations) {
  try {
    const cacheKey = `translations_${language}`;
    localStorage.setItem(cacheKey, JSON.stringify(translations));
  } catch (error) {
    console.warn('Failed to cache translations:', error);
  }
}
```

#### **HTTP Caching**
- Translation files served with cache headers
- ETags for efficient cache validation
- CDN distribution for global performance
- Gzip compression for smaller file sizes

## 🧪 Testing & Quality Assurance

### **Automated Testing**

#### **Unit Tests**
```bash
# Run i18n-specific tests
npm test -- tests/unit/i18n.test.js

# Run all tests with coverage
npm test -- --coverage

# Watch mode for development
npm run test:watch -- tests/unit/i18n.test.js
```

#### **Test Examples**
```javascript
// tests/unit/i18n.test.js
import { I18nService } from '../../src/scripts/services/I18nService.js';

describe('I18nService', () => {
  let i18nService;

  beforeEach(() => {
    i18nService = new I18nService({
      defaultLanguage: 'nl',
      fallbackLanguage: 'nl'
    });
  });

  test('should detect browser language', () => {
    // Mock navigator.language
    Object.defineProperty(navigator, 'language', {
      value: 'en-US',
      configurable: true
    });

    const detected = i18nService.detectLanguage();
    expect(detected).toBe('en');
  });

  test('should fallback to default language for unsupported language', () => {
    const detected = i18nService.detectLanguage();
    expect(['nl', 'en', 'fr', 'pt']).toContain(detected);
  });

  test('should interpolate parameters in translations', () => {
    const result = i18nService.interpolate('Hello {{name}}!', { name: 'John' });
    expect(result).toBe('Hello John!');
  });
});
```

### **Manual Testing Procedures**

#### **Functional Testing Checklist**
- [ ] **Language Detection**
  - [ ] URL parameter detection (`?lang=en`)
  - [ ] localStorage persistence works
  - [ ] Browser language detection functions
  - [ ] Default fallback to Dutch

- [ ] **Language Switching**
  - [ ] Dropdown opens and closes properly
  - [ ] All languages selectable
  - [ ] Content updates without page reload
  - [ ] URL updates with language parameter
  - [ ] Choice persists across page refreshes

- [ ] **Content Translation**
  - [ ] Navigation menu translates
  - [ ] Hero section updates
  - [ ] Service descriptions change
  - [ ] FAQ content translates
  - [ ] Form labels and messages update
  - [ ] Footer content changes

- [ ] **Accessibility Testing**
  - [ ] Keyboard navigation works
  - [ ] Screen reader announcements
  - [ ] Focus management maintained
  - [ ] ARIA attributes correct

#### **Cross-Browser Testing**
```bash
# Test in multiple browsers
# Chrome, Firefox, Safari, Edge
# Mobile browsers (iOS Safari, Chrome Mobile)

# Test with different language settings
# Browser language: Dutch, English, French, Portuguese
# System language different from browser language
```

#### **Performance Testing**
```bash
# Measure translation loading times
# Check memory usage with multiple language switches
# Verify no memory leaks during language switching
# Test with slow network connections
```

## 🔧 Troubleshooting Guide

### **Common Issues & Solutions**

#### **1. Translations Not Loading**

**Symptoms:**
- Content remains in default language
- Console errors about failed requests
- Missing translation keys

**Solutions:**
```bash
# Check translation file paths
ls -la src/data/i18n/
# Should show: nl.json, en.json, fr.json, pt.json

# Verify JSON syntax
npm run lint:json  # If available
# Or use online JSON validator

# Check browser console for errors
# Look for 404 errors or parsing errors

# Verify file permissions
chmod 644 src/data/i18n/*.json
```

#### **2. Language Not Persisting**

**Symptoms:**
- Language resets on page refresh
- URL parameter not updating
- localStorage not saving

**Solutions:**
```javascript
// Check localStorage permissions
try {
  localStorage.setItem('test', 'test');
  localStorage.removeItem('test');
  console.log('localStorage available');
} catch (error) {
  console.error('localStorage not available:', error);
}

// Verify URL parameter handling
const urlParams = new URLSearchParams(window.location.search);
console.log('Current lang parameter:', urlParams.get('lang'));

// Check for conflicting scripts
// Ensure no other scripts are modifying localStorage
```

#### **3. Missing Translations**

**Symptoms:**
- Some content shows translation keys instead of text
- Inconsistent translations across languages
- Fallback not working

**Solutions:**
```javascript
// Check translation key structure
const translations = await fetch('/src/data/i18n/en.json').then(r => r.json());
console.log('Available keys:', Object.keys(translations));

// Verify nested object structure
console.log('Navigation keys:', translations.nav);

// Test fallback mechanism
const i18nService = new I18nService({ debug: true });
const result = i18nService.t('non.existent.key');
console.log('Fallback result:', result);
```

#### **4. Performance Issues**

**Symptoms:**
- Slow language switching
- High memory usage
- UI freezing during translation

**Solutions:**
```javascript
// Enable performance monitoring
const i18nService = new I18nService({
  debug: true,
  performanceMonitoring: true
});

// Check translation file sizes
ls -lh src/data/i18n/
# Files should be < 50KB each

// Monitor memory usage
console.log('Translation cache size:', i18nService.translations.size);

// Use performance profiling
performance.mark('language-switch-start');
await i18nService.switchLanguage('en');
performance.mark('language-switch-end');
performance.measure('language-switch', 'language-switch-start', 'language-switch-end');
```

### **Debug Mode & Logging**

#### **Enable Debug Mode**
```javascript
const i18nService = new I18nService({
  defaultLanguage: 'nl',
  fallbackLanguage: 'nl',
  debug: true  // Enable detailed logging
});

// Debug output will show:
// - Language detection process
// - Translation file loading
// - Missing translation warnings
// - Performance metrics
```

#### **Custom Debug Logging**
```javascript
// Add custom logging to track issues
class DebugI18nService extends I18nService {
  t(key, params = {}) {
    const result = super.t(key, params);

    if (this.options.debug) {
      console.log(`Translation: ${key} -> ${result}`);
      if (result === key) {
        console.warn(`Missing translation for key: ${key}`);
      }
    }

    return result;
  }
}
```

### **Development Tools**

#### **Browser Extensions**
- **Vue.js devtools**: For debugging reactive translations
- **React Developer Tools**: If using React components
- **Accessibility Insights**: For accessibility testing
- **Language Switcher Tester**: Custom extension for testing

#### **Console Commands**
```javascript
// Available in browser console (development mode)
window.TechSupportApp.getService('i18n').switchLanguage('en');
window.TechSupportApp.getService('i18n').getCurrentLanguage();
window.TechSupportApp.getService('i18n').getAvailableLanguages();

// Debug translation loading
window.TechSupportApp.getService('i18n').loadLanguage('fr').then(console.log);

// Test translation keys
window.TechSupportApp.getService('i18n').t('nav.home');
```

## 🌍 Adding New Languages

### **Step-by-Step Guide**

#### **1. Create Translation File**
```bash
# Copy existing translation file as template
cp src/data/i18n/nl.json src/data/i18n/de.json

# Or create from scratch with same structure
touch src/data/i18n/de.json
```

#### **2. Translate Content**
```json
// src/data/i18n/de.json
{
  "nav": {
    "home": "Startseite",
    "services": "Dienstleistungen",
    "about": "Über uns",
    "testimonials": "Referenzen",
    "blog": "Blog",
    "contact": "Kontakt"
  },
  "hero": {
    "title": "IT-Support auf menschlicher Ebene",
    "subtitle": "Direkte Hilfe bei Computer- und Netzwerkproblemen ohne Fachjargon.",
    "cta": "Kostenlose Beratung anfordern",
    "secondary": "Unsere Dienstleistungen"
  },
  // ... continue with all translation keys
}
```

**Translation Guidelines:**
- Maintain exact same key structure as other language files
- Keep parameter placeholders (`{{variable}}`) unchanged
- Preserve HTML entities and special characters
- Consider cultural context and local terminology
- Test character encoding (UTF-8)

#### **3. Add Flag Icon**
```bash
# Add SVG flag icon (recommended size: 24x16px)
# Download from reliable source or create custom
cp flag-sources/de.svg src/assets/images/flags/de.svg

# Optimize SVG
npx svgo src/assets/images/flags/de.svg
```

#### **4. Update Language Configuration**
```javascript
// src/scripts/services/I18nService.js
isValidLanguage(lang) {
  const supportedLanguages = ['nl', 'en', 'fr', 'pt', 'de']; // Add 'de'
  return supportedLanguages.includes(lang);
}

getAvailableLanguages() {
  return ['nl', 'en', 'fr', 'pt', 'de']; // Add 'de'
}
```

#### **5. Update Language Switcher UI**
```html
<!-- Add to src/index.html language dropdown -->
<li class="nav-dropdown-item" role="none">
  <a href="#" role="menuitem" class="nav-link" data-lang="de">
    <img src="/src/assets/images/flags/de.svg" alt="" aria-hidden="true" class="flag-icon">
    <span class="language-text">Deutsch</span>
  </a>
</li>
```

#### **6. Update Language Names Map**
```javascript
// src/scripts/services/I18nService.js
updateLanguageSwitcher() {
  const languageNames = {
    nl: 'Nederlands',
    en: 'English',
    fr: 'Français',
    pt: 'Português',
    de: 'Deutsch'  // Add German
  };
  // ... rest of method
}
```

#### **7. Test New Language**
```bash
# Test language detection
# Add ?lang=de to URL and verify content loads

# Test language switching
# Use language dropdown to switch to German

# Test persistence
# Refresh page and verify German remains selected

# Test fallback
# Remove some translation keys and verify fallback works
```

### **Quality Assurance for New Languages**

#### **Translation Review Checklist**
- [ ] All translation keys present and translated
- [ ] Cultural appropriateness verified
- [ ] Technical terminology accurate
- [ ] Grammar and spelling checked
- [ ] Character encoding correct (UTF-8)
- [ ] Parameter placeholders preserved
- [ ] HTML entities properly escaped
- [ ] Length considerations for UI elements

#### **Technical Integration Checklist**
- [ ] Translation file valid JSON
- [ ] Flag icon added and optimized
- [ ] Language code added to configuration
- [ ] UI dropdown updated
- [ ] Language names map updated
- [ ] URL parameter handling works
- [ ] localStorage persistence functions
- [ ] Fallback mechanism tested
- [ ] Accessibility attributes correct

#### **Testing Checklist**
- [ ] Manual language switching works
- [ ] URL parameter detection functions
- [ ] Content updates without page reload
- [ ] Persistence across browser sessions
- [ ] Mobile interface works correctly
- [ ] Keyboard navigation functional
- [ ] Screen reader compatibility verified
- [ ] Performance impact minimal

## 📋 Best Practices & Guidelines

### **Translation Key Management**

#### **Naming Conventions**
```javascript
// ✅ Good: Descriptive, hierarchical keys
"nav.services"
"hero.title"
"services.software.title"
"contact.form.email.placeholder"

// ❌ Avoid: Generic or unclear keys
"text1"
"button"
"error"
"thing"
```

#### **Structure Guidelines**
- **Maximum 3 levels deep**: Avoid overly nested structures
- **Consistent hierarchy**: Same structure across all languages
- **Logical grouping**: Group related content together
- **Descriptive names**: Keys should indicate content purpose

#### **Key Maintenance**
```bash
# Use tools to validate key consistency
npm run validate-translations  # Custom script to check key parity

# Example validation script
node tools/validate-i18n.js
```

### **Content Management Best Practices**

#### **Translation Synchronization**
- **Version Control**: Track translation changes in Git
- **Translation Memory**: Maintain consistency across similar content
- **Review Process**: Implement translation review workflow
- **Update Tracking**: Document when translations were last updated

#### **Dynamic Content Handling**
```javascript
// ✅ Good: Use parameters for dynamic content
"welcome.message": "Welcome {{name}} to {{company}}"

// ❌ Avoid: Hardcoded dynamic content
"welcome.message": "Welcome John to TechSupport Pro"
```

#### **Cultural Considerations**
- **Local Terminology**: Use region-appropriate technical terms
- **Cultural Context**: Adapt content for local business practices
- **Legal Requirements**: Consider local legal and privacy requirements
- **Currency & Dates**: Format according to local conventions

### **Performance Best Practices**

#### **File Size Optimization**
```bash
# Keep translation files under 50KB each
ls -lh src/data/i18n/
# nl.json: 15KB, en.json: 16KB, fr.json: 17KB, pt.json: 16KB

# Minimize JSON file size
npm run minify-translations  # Remove unnecessary whitespace
```

#### **Loading Strategy**
```javascript
// Preload critical languages
const criticalLanguages = ['nl', 'en'];  // Most common
await Promise.all(criticalLanguages.map(lang => i18nService.loadLanguage(lang)));

// Lazy load other languages
const otherLanguages = ['fr', 'pt'];
// Load on demand when user switches
```

#### **Caching Strategy**
```javascript
// Implement intelligent caching
class CachedI18nService extends I18nService {
  async loadLanguage(language) {
    // Check memory cache first
    if (this.memoryCache.has(language)) {
      return this.memoryCache.get(language);
    }

    // Check localStorage cache
    const cached = this.getFromLocalStorage(language);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.data;
    }

    // Load from server as last resort
    return super.loadLanguage(language);
  }
}
```

### **Accessibility Best Practices**

#### **Language Attributes**
```html
<!-- Always set document language -->
<html lang="nl">

<!-- Update when language changes -->
<script>
document.addEventListener('languagechange', (event) => {
  document.documentElement.lang = event.detail.language;
});
</script>
```

#### **Screen Reader Support**
```javascript
// Provide meaningful announcements
announceLanguageChange(newLang, oldLang) {
  const message = this.t('accessibility.language_changed', {
    from: this.getLanguageName(oldLang),
    to: this.getLanguageName(newLang)
  });

  this.announce(message, 'polite');
}
```

#### **Fallback Content**
```html
<!-- Always provide fallback text -->
<h1 data-i18n="hero.title">IT Ondersteuning op Menselijke Maat</h1>

<!-- Not just empty elements -->
<h1 data-i18n="hero.title"></h1> <!-- ❌ Bad -->
```

## 📚 API Reference

### **I18nService Complete API**

#### **Core Methods**
```javascript
// Initialization
await i18nService.init()

// Language Management
const language = i18nService.detectLanguage()
await i18nService.loadLanguage('en')
await i18nService.switchLanguage('fr')
const current = i18nService.getCurrentLanguage()
const available = i18nService.getAvailableLanguages()

// Translation
const text = i18nService.t('nav.home')
const withParams = i18nService.t('welcome.message', { name: 'John' })

// Utility
const isValid = i18nService.isValidLanguage('de')
const isLoading = i18nService.isServiceLoading()
i18nService.applyTranslations()
await i18nService.preloadLanguages()
```

#### **Configuration Options**
```javascript
const i18nService = new I18nService({
  defaultLanguage: 'nl',        // Default language
  fallbackLanguage: 'nl',       // Fallback for missing translations
  storageKey: 'app_language',   // localStorage key
  debug: false,                 // Enable debug logging
  autoApply: true,              // Auto-apply translations on load
  preloadCritical: ['nl', 'en'] // Languages to preload
});
```

### **LanguageSwitcher Complete API**

#### **Core Methods**
```javascript
// Initialization
languageSwitcher.init()

// UI Management
languageSwitcher.toggleDropdown()
languageSwitcher.openDropdown()
languageSwitcher.closeDropdown()
languageSwitcher.updateUI()

// Language Operations
await languageSwitcher.switchLanguage('en')
const current = languageSwitcher.getCurrentLanguage()

// Cleanup
languageSwitcher.destroy()
```

## 🔗 Integration Examples

### **Form Validation Integration**
```javascript
// Update validation messages when language changes
document.addEventListener('languagechange', (event) => {
  const validationService = app.getService('validation');
  validationService.updateLanguage(event.detail.language);

  // Re-validate current form if needed
  const activeForm = document.querySelector('form.validating');
  if (activeForm) {
    validationService.revalidateForm(activeForm);
  }
});
```

### **Analytics Integration**
```javascript
// Track language usage and switches
document.addEventListener('languagechange', (event) => {
  // Google Analytics 4
  gtag('event', 'language_change', {
    previous_language: event.detail.previousLanguage,
    new_language: event.detail.language,
    user_type: 'returning_visitor'
  });

  // Custom analytics
  analytics.track('Language Changed', {
    from: event.detail.previousLanguage,
    to: event.detail.language,
    method: 'dropdown_selection',
    timestamp: new Date().toISOString()
  });
});
```

### **SEO Integration**
```javascript
// Update meta tags when language changes
document.addEventListener('languagechange', (event) => {
  const lang = event.detail.language;

  // Update page title
  document.title = i18nService.t('meta.title');

  // Update meta description
  const metaDesc = document.querySelector('meta[name="description"]');
  if (metaDesc) {
    metaDesc.content = i18nService.t('meta.description');
  }

  // Update Open Graph tags
  const ogTitle = document.querySelector('meta[property="og:title"]');
  if (ogTitle) {
    ogTitle.content = i18nService.t('meta.og_title');
  }
});
```

### **Error Handling Integration**
```javascript
// Provide localized error messages
class LocalizedErrorHandler {
  constructor(i18nService) {
    this.i18n = i18nService;
  }

  handleError(error, context) {
    const errorKey = `errors.${error.type || 'generic'}`;
    const message = this.i18n.t(errorKey, {
      context: context,
      timestamp: new Date().toLocaleString()
    });

    this.showErrorMessage(message);
  }
}
```

---

**🌐 This multilanguage system provides a robust, accessible, and user-friendly internationalization solution that scales with your global audience needs.**
