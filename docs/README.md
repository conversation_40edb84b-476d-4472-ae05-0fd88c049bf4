# TechSupport Pro Website

Modern, accessible, and performant website for TechSupport Pro - a Dutch IT support company. Built with cutting-edge web technologies and following industry best practices for performance, accessibility, and maintainability.

## ✨ Features

### 🏗️ **Modern Architecture**
- **ES6+ Modules**: Clean, modular JavaScript architecture
- **Vite 5.0**: Lightning-fast build system with HMR
- **SCSS with 7-1 Architecture**: Organized, maintainable stylesheets
- **Component-Based Design**: Reusable, testable components
- **Service Layer**: Separated business logic and utilities

### ♿ **Accessibility First**
- **WCAG 2.1 AA Compliant**: Comprehensive accessibility implementation
- **Keyboard Navigation**: Full keyboard support with shortcuts
- **Screen Reader Support**: ARIA labels and live regions
- **Focus Management**: Logical focus flow and skip links
- **Automated Testing**: Pa11y integration for continuous accessibility testing

### ⚡ **Performance Optimized**
- **Lighthouse Scores 95+**: Optimized for Core Web Vitals
- **Code Splitting**: Automatic chunking for optimal loading
- **Image Optimization**: WebP/AVIF support with fallbacks
- **Service Worker**: Offline functionality and caching
- **Lazy Loading**: Intersection Observer for performance

### 🌐 **Internationalization**
- **Multi-language Support**: Dutch, English, French, Portuguese
- **Dynamic Language Switching**: Real-time content translation
- **Persistent Preferences**: localStorage and URL parameter support
- **Fallback System**: Graceful handling of missing translations
- **Browser Language Detection**: Automatic language selection

### 📱 **Responsive & Mobile-First**
- **Mobile-First Design**: Optimized for all device sizes
- **Touch-Friendly**: Accessible touch targets and gestures
- **Progressive Enhancement**: Works without JavaScript
- **Flexible Grid System**: CSS Grid and Flexbox layouts

### 🔍 **SEO Optimized**
- **Semantic HTML**: Proper document structure and landmarks
- **Meta Tags**: Complete Open Graph and Twitter Card support
- **Structured Data**: Schema.org markup ready
- **Sitemap Generation**: Automated XML sitemap creation
- **Performance**: Fast loading for better search rankings

## 🚀 Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- npm 9.0.0 or higher

### Installation & Development

```bash
# Clone the repository
git clone <repository-url>
cd techsupport-pro

# Install dependencies
npm install

# Start development server (http://localhost:3000)
npm run dev

# Build for production
npm run build

# Preview production build (http://localhost:4173)
npm run preview
```

## 📁 Project Structure

```
techsupport-pro/
├── src/                          # 🎯 Source files (development)
│   ├── assets/                   # 🖼️ Static assets
│   │   ├── images/              # Images (original and optimized)
│   │   ├── icons/               # SVG icons and favicons
│   │   └── fonts/               # Local font files
│   ├── styles/                   # 🎨 SCSS stylesheets (7-1 architecture)
│   │   ├── abstracts/           # Variables, mixins, functions
│   │   ├── base/                # Reset, typography, base styles
│   │   ├── components/          # Reusable UI components
│   │   ├── layout/              # Header, footer, navigation, grid
│   │   ├── pages/               # Page-specific styles
│   │   ├── themes/              # Theme variations (light/dark)
│   │   ├── utilities/           # Helper classes
│   │   └── main.scss            # Main stylesheet entry point
│   ├── scripts/                  # ⚡ JavaScript modules (ES6+)
│   │   ├── components/          # UI component classes
│   │   ├── services/            # Business logic and data services
│   │   ├── utils/               # Utility functions and helpers
│   │   ├── config/              # Configuration files
│   │   └── main.js              # Application entry point
│   ├── templates/                # 📄 HTML templates and components
│   ├── data/                     # 📊 Static data files
│   │   └── i18n/                # Translation files (nl, en, fr, pt)
│   ├── content/                  # 📝 Content management
│   └── index.html               # Main HTML entry point
├── public/                       # 🌐 Static files (copied to dist)
├── dist/                         # 📦 Production build (generated)
├── tests/                        # 🧪 Test files
├── docs/                         # 📚 Documentation
├── tools/                        # 🔧 Build tools and scripts
└── reports/                      # 📊 Generated reports
```

For detailed structure explanation, see [RECOMMENDED_STRUCTURE.md](RECOMMENDED_STRUCTURE.md).

## 🛠️ Technology Stack

### **Core Technologies**
- **Build Tool**: Vite 5.0 (fast, modern bundler)
- **Styling**: SCSS with 7-1 architecture pattern
- **JavaScript**: ES6+ modules, vanilla JS (framework-free)
- **HTML**: Semantic HTML5 with accessibility features

### **Development & Quality**
- **Testing**: Jest (unit), Pa11y (accessibility), Lighthouse (performance)
- **Code Quality**: ESLint, Prettier, automated formatting
- **Performance**: Core Web Vitals optimization, image optimization
- **Accessibility**: WCAG 2.1 AA compliance, automated testing

### **Features & Integrations**
- **Internationalization**: Custom i18n service with 4 languages
- **PWA**: Service Worker, offline functionality, web manifest
- **SEO**: Structured data, meta tags, sitemap generation
- **Analytics**: Performance monitoring, user journey tracking

## 📊 Performance Metrics & Targets

### **Lighthouse Scores**
- **Performance**: 95+ (Target: Excellent Core Web Vitals)
- **Accessibility**: 100 (WCAG 2.1 AA compliant)
- **Best Practices**: 95+ (Modern web standards)
- **SEO**: 100 (Optimized for search engines)
- **PWA**: 90+ (Progressive Web App features)

### **Core Web Vitals**
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1
- **FCP (First Contentful Paint)**: < 1.8s
- **TTI (Time to Interactive)**: < 3.8s

## 🎯 Browser Support

### **Fully Supported**
- Chrome 90+ (Chromium-based browsers)
- Firefox 88+ (Gecko engine)
- Safari 14+ (WebKit engine)
- Edge 90+ (Chromium-based)

### **Progressive Enhancement**
- Graceful degradation for older browsers
- Core functionality works without JavaScript
- CSS fallbacks for modern features

## 📚 Documentation

### **Getting Started**
- [Setup Guide](SETUP.md) - Complete development environment setup
- [Deployment Guide](DEPLOYMENT.md) - Production deployment instructions
- [Project Structure](RECOMMENDED_STRUCTURE.md) - Detailed architecture overview

### **Development Guides**
- [Accessibility Guide](accessibility-guide.md) - WCAG compliance and testing
- [UX Guidelines](ux-guidelines.md) - Design system and user experience
- [Multilanguage System](MULTILANGUAGE_SYSTEM.md) - i18n implementation
- [API Documentation](API.md) - JavaScript components and services

### **Quality Assurance**
- [Testing Guide](TESTING.md) - Testing strategies and procedures
- [Performance Guide](PERFORMANCE.md) - Optimization techniques
- [Scripts Documentation](SCRIPTS.md) - NPM scripts reference

## 🧪 Testing & Quality Assurance

### **Automated Testing**
```bash
# Run all tests
npm test

# Watch mode for development
npm run test:watch

# Test coverage report
npm run test:coverage

# Accessibility testing
npm run a11y-test

# Performance audit
npm run lighthouse
```

### **Code Quality**
```bash
# Lint JavaScript
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Check formatting
npm run format:check
```

## 🔧 Development Workflow

### **Asset Management**
```bash
# Optimize images (WebP, AVIF conversion)
npm run optimize-images

# Generate sitemap
npm run generate-sitemap

# Clean build directory
npm run clean

# Build and serve
npm run serve
```

### **Development Server**
```bash
# Start development server with HMR
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🚀 Deployment

### **Build Process**
1. **Code Quality**: Automated linting and formatting
2. **Testing**: Unit tests and accessibility validation
3. **Optimization**: Image compression, code minification
4. **Performance**: Lighthouse auditing and Core Web Vitals
5. **SEO**: Sitemap generation and meta tag validation

### **Production Checklist**
- [ ] All tests passing
- [ ] Lighthouse scores meet targets
- [ ] Accessibility compliance verified
- [ ] Images optimized
- [ ] Sitemap generated
- [ ] Environment variables configured

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### **Development Standards**
- Follow ESLint and Prettier configurations
- Write tests for new features
- Ensure accessibility compliance
- Update documentation for changes
- Maintain performance standards

## 📄 License

© 2024 TechSupport Pro. All rights reserved.

---

**Built with ❤️ using modern web technologies**
