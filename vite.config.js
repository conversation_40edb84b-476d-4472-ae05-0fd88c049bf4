import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  root: 'src',
  base: '/',

  build: {
    outDir: '../dist',
    emptyOutDir: true,

    // Performance optimizations
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/index.html')
      },
      
      output: {
        // Code splitting for better caching
        manualChunks: {
          vendor: ['intersection-observer'],
          utils: [
            './src/scripts/utils/dom.js',
            './src/scripts/utils/performance.js'
          ],
          components: [
            './src/scripts/components/ContactForm.js',
            './src/scripts/components/Navigation.js',
            './src/scripts/components/LanguageSwitcher.js'
          ],
          services: [
            './src/scripts/services/I18nService.js',
            './src/scripts/services/ValidationService.js',
            './src/scripts/services/ContactService.js'
          ]
        },
        
        // Asset naming for better caching
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash].${ext}`;
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
            return `assets/fonts/[name]-[hash].${ext}`;
          }
          
          if (/\.css$/i.test(assetInfo.name)) {
            return `assets/css/[name]-[hash].${ext}`;
          }
          
          return `assets/[name]-[hash].${ext}`;
        }
      }
    },
    
    // Minification and optimization
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    
    // Source maps for debugging
    sourcemap: process.env.NODE_ENV === 'development',
    
    // Asset optimization
    assetsInlineLimit: 4096, // 4kb
    
    // CSS code splitting
    cssCodeSplit: true,
    
    // Target modern browsers
    target: 'es2015'
  },
  
  // Development server
  server: {
    port: 3000,
    open: true,
    cors: true,
    
    // Proxy for API calls during development
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // Preview server (for production build testing)
  preview: {
    port: 4173,
    open: true
  },
  
  // CSS preprocessing
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use "./src/styles/abstracts/variables" as *;
          @use "./src/styles/abstracts/mixins" as *;
        `,
        includePaths: ['src/styles']
      }
    },

    // PostCSS plugins will be loaded from postcss.config.js
  },
  
  // Plugin configuration
  plugins: [
    // Custom plugin for HTML processing
    {
      name: 'html-transform',
      transformIndexHtml: {
        enforce: 'pre',
        transform(html, context) {
          // Add performance hints and ensure proper asset paths
          return html
            .replace(
              '<head>',
              `<head>
                <link rel="preconnect" href="https://fonts.googleapis.com">
                <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
                <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta name="theme-color" content="#0056b3">
              `
            )
            // Ensure asset paths are correct
            .replace(/href="\/images\//g, 'href="/assets/images/')
            .replace(/src="\/images\//g, 'src="/assets/images/');
        }
      }
    },
    
    // Service Worker plugin
    {
      name: 'sw-plugin',
      generateBundle() {
        // Copy service worker to output
        this.emitFile({
          type: 'asset',
          fileName: 'sw.js',
          source: `
            const CACHE_NAME = 'techsupport-pro-v2';
            const urlsToCache = [
              '/',
              '/assets/css/main.css',
              '/assets/js/main.js',
              '/assets/images/logo/techsupport-logo.svg',
              '/offline.html'
            ];
            
            self.addEventListener('install', (event) => {
              event.waitUntil(
                caches.open(CACHE_NAME)
                  .then((cache) => cache.addAll(urlsToCache))
              );
            });
            
            self.addEventListener('fetch', (event) => {
              event.respondWith(
                caches.match(event.request)
                  .then((response) => {
                    if (response) {
                      return response;
                    }
                    return fetch(event.request);
                  })
                  .catch(() => {
                    if (event.request.destination === 'document') {
                      return caches.match('/offline.html');
                    }
                  })
              );
            });
          `
        });
      }
    }
  ],
  
  // Optimization
  optimizeDeps: {
    include: ['intersection-observer']
  },
  
  // Environment variables
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_DATE__: JSON.stringify(new Date().toISOString())
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/scripts/components'),
      '@services': resolve(__dirname, 'src/scripts/services'),
      '@utils': resolve(__dirname, 'src/scripts/utils'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  }
});
