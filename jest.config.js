export default {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/src/**/*.test.js'
  ],
  collectCoverageFrom: [
    'src/scripts/**/*.js',
    '!src/scripts/**/*.test.js',
    '!src/scripts/main.js'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/scripts/components/$1',
    '^@services/(.*)$': '<rootDir>/src/scripts/services/$1',
    '^@utils/(.*)$': '<rootDir>/src/scripts/utils/$1'
  },
  transform: {
    '^.+\\.js$': 'babel-jest'
  }
};
