# TechSupport Pro Website

Modern, accessible, and performant website for TechSupport Pro - a Dutch IT support company. Built with cutting-edge web technologies and following industry best practices.

## ✨ Key Features

- **🏗️ Modern Architecture**: ES6+ modules, Vite 5.0, SCSS with 7-1 pattern
- **♿ Accessibility First**: WCAG 2.1 AA compliant with automated testing
- **⚡ Performance Optimized**: Lighthouse scores 95+, Core Web Vitals optimized
- **🌐 Internationalization**: 4 languages (Dutch, English, French, Portuguese)
- **📱 Responsive Design**: Mobile-first approach with progressive enhancement
- **🔍 SEO Optimized**: Semantic HTML, structured data, automated sitemap

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

**Prerequisites**: Node.js 18+ and npm 9+

## 📚 Complete Documentation

For comprehensive documentation, please visit the [`docs/`](docs/) directory:

### **📖 Getting Started**
- **[Documentation Index](docs/INDEX.md)** - Complete documentation overview
- **[Complete README](docs/README.md)** - Full project overview and features
- **[Setup Guide](docs/SETUP.md)** - Development environment setup
- **[Project Structure](docs/RECOMMENDED_STRUCTURE.md)** - Architecture overview

### **🔧 Development**
- **[API Documentation](docs/API.md)** - JavaScript components and services
- **[Scripts Reference](docs/SCRIPTS.md)** - NPM scripts documentation
- **[Testing Guide](docs/TESTING.md)** - Testing strategies and procedures

### **🎯 Quality & Performance**
- **[Accessibility Guide](docs/accessibility-guide.md)** - WCAG compliance
- **[Performance Guide](docs/PERFORMANCE.md)** - Optimization techniques
- **[UX Guidelines](docs/ux-guidelines.md)** - Design system

### **🌐 Features**
- **[Multilanguage System](docs/MULTILANGUAGE_SYSTEM.md)** - i18n implementation
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment

## 📊 Performance Targets

- **Lighthouse Performance**: 95+ | **Accessibility**: 100 | **SEO**: 100
- **Core Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1

## 🛠️ Technology Stack

- **Build**: Vite 5.0 | **Styling**: SCSS | **JavaScript**: ES6+ modules
- **Testing**: Jest, Pa11y, Lighthouse | **Quality**: ESLint, Prettier

## 📁 Project Structure

See [docs/RECOMMENDED_STRUCTURE.md](docs/RECOMMENDED_STRUCTURE.md) for detailed project structure.

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### **Development Standards**
- Follow ESLint and Prettier configurations
- Write tests for new features
- Ensure accessibility compliance
- Update documentation for changes
- Maintain performance standards

## 📄 License

© 2024 TechSupport Pro. All rights reserved.

---

**Built with ❤️ using modern web technologies**