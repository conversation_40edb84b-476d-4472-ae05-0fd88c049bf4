var e=Object.defineProperty,t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,s=(t,n,i)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[n]=i,a=(e,a)=>{for(var r in a||(a={}))n.call(a,r)&&s(e,r,a[r]);if(t)for(var r of t(a))i.call(a,r)&&s(e,r,a[r]);return e},r=(e,t,n)=>new Promise((i,s)=>{var a=e=>{try{o(n.next(e))}catch(t){s(t)}},r=e=>{try{o(n.throw(e))}catch(t){s(t)}},o=e=>e.done?i(e.value):Promise.resolve(e.value).then(a,r);o((n=n.apply(e,t)).next())});import{V as o,N as c,C as l}from"./components-pKGiqqTQ.js";import{r as u,$ as g,p as d,a as h,b as p}from"./utils-2gcXUZnL.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();class f{constructor(e,t,n={}){this.element=e,this.i18nService=t,this.options=a({showFlags:!0,showText:!0},n),this.isOpen=!1,this.init()}init(){this.element&&this.i18nService&&(this.setupElements(),this.bindEvents(),this.updateUI())}setupElements(){this.toggle=this.element.querySelector(".nav-link"),this.menu=this.element.querySelector(".nav-dropdown-menu"),this.currentLangElement=this.element.querySelector(".current-language"),this.langLinks=this.element.querySelectorAll("[data-lang]")}bindEvents(){this.toggle&&this.toggle.addEventListener("click",e=>{e.preventDefault(),this.toggleDropdown()}),this.langLinks.forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const n=e.getAttribute("data-lang");this.switchLanguage(n)})}),document.addEventListener("click",e=>{this.element.contains(e.target)||this.closeDropdown()}),this.element.addEventListener("keydown",this.handleKeydown.bind(this)),document.addEventListener("languagechange",this.handleLanguageChange.bind(this))}toggleDropdown(){this.isOpen?this.closeDropdown():this.openDropdown()}openDropdown(){this.isOpen=!0,this.menu.classList.add("active"),this.toggle.setAttribute("aria-expanded","true");const e=this.menu.querySelector(".nav-link");e&&e.focus()}closeDropdown(){this.isOpen=!1,this.menu.classList.remove("active"),this.toggle.setAttribute("aria-expanded","false")}switchLanguage(e){return r(this,null,function*(){if(!this.i18nService.isServiceLoading())try{this.setLoadingState(!0),yield this.i18nService.switchLanguage(e),this.closeDropdown(),this.updateUI()}catch(t){this.showError("Failed to switch language. Please try again.")}finally{this.setLoadingState(!1)}})}handleKeydown(e){const{key:t}=e;if("Escape"===t)return this.closeDropdown(),void this.toggle.focus();if(!this.isOpen)return;const n=Array.from(this.langLinks),i=n.indexOf(document.activeElement);if("ArrowDown"===t){e.preventDefault();n[i<n.length-1?i+1:0].focus()}else if("ArrowUp"===t){e.preventDefault();n[i>0?i-1:n.length-1].focus()}else if(("Enter"===t||" "===t)&&(e.preventDefault(),document.activeElement.hasAttribute("data-lang"))){const e=document.activeElement.getAttribute("data-lang");this.switchLanguage(e)}}handleLanguageChange(e){this.updateUI()}updateUI(){const e=this.i18nService.getCurrentLanguage();if(this.currentLangElement){const t={nl:"Nederlands",en:"English",fr:"Français"};this.currentLangElement.textContent=t[e]}this.langLinks.forEach(t=>{t.getAttribute("data-lang")===e?(t.classList.add("active"),t.setAttribute("aria-current","true")):(t.classList.remove("active"),t.removeAttribute("aria-current"))}),this.options.showFlags&&this.updateFlags()}updateFlags(){const e=this.i18nService.getCurrentLanguage(),t=this.toggle.querySelector(".flag-icon");t&&(t.src=`/src/assets/images/flags/${e}.svg`,t.alt=`${e.toUpperCase()} flag`)}setLoadingState(e){e?(this.element.classList.add("loading"),this.toggle.setAttribute("aria-busy","true")):(this.element.classList.remove("loading"),this.toggle.setAttribute("aria-busy","false"))}showError(e){const t=document.createElement("div");t.className="language-error",t.textContent=e,t.setAttribute("role","alert"),t.style.cssText="\n      position: absolute;\n      top: 100%;\n      left: 0;\n      right: 0;\n      background: #dc3545;\n      color: white;\n      padding: 8px 12px;\n      border-radius: 4px;\n      font-size: 14px;\n      z-index: 1000;\n      margin-top: 4px;\n    ",this.element.style.position="relative",this.element.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)}getCurrentLanguage(){return this.i18nService.getCurrentLanguage()}destroy(){document.removeEventListener("click",this.handleOutsideClick),document.removeEventListener("languagechange",this.handleLanguageChange),this.langLinks.forEach(e=>{e.removeEventListener("click",this.handleLanguageClick)}),this.toggle&&this.toggle.removeEventListener("click",this.handleToggleClick)}}class m{constructor(e={}){this.options=a({defaultLanguage:"nl",fallbackLanguage:"nl",storageKey:"techsupport_language"},e),this.currentLanguage=this.options.defaultLanguage,this.translations=new Map,this.isLoading=!1,this.init()}init(){return r(this,null,function*(){this.currentLanguage=this.detectLanguage(),yield this.loadLanguage(this.currentLanguage),this.applyTranslations(),document.documentElement.lang=this.currentLanguage,this.saveLanguagePreference()})}detectLanguage(){const e=new URLSearchParams(window.location.search).get("lang");if(e&&this.isValidLanguage(e))return e;const t=localStorage.getItem(this.options.storageKey);if(t&&this.isValidLanguage(t))return t;const n=navigator.language.split("-")[0];return this.isValidLanguage(n)?n:this.options.defaultLanguage}isValidLanguage(e){return["nl","en","fr"].includes(e)}loadLanguage(e){return r(this,null,function*(){if(this.translations.has(e))return this.translations.get(e);this.isLoading=!0;try{const n=[`/src/data/i18n/${e}.json`,`/data/i18n/${e}.json`,`./src/data/i18n/${e}.json`];let i,s;for(const e of n)try{if(i=yield fetch(e),i.ok)break}catch(t){s=t}if(!i||!i.ok)throw new Error(`Failed to load language file: ${e}`);const a=yield i.json();return this.translations.set(e,a),a}catch(n){if(e!==this.options.fallbackLanguage)return this.loadLanguage(this.options.fallbackLanguage);throw n}finally{this.isLoading=!1}})}switchLanguage(e){return r(this,null,function*(){this.isValidLanguage(e)&&e!==this.currentLanguage&&(yield this.loadLanguage(e),this.currentLanguage=e,this.applyTranslations(),document.documentElement.lang=e,this.saveLanguagePreference(),this.updateURL(),this.updateLanguageSwitcher(),this.triggerLanguageChangeEvent())})}t(e,t={}){const n=this.translations.get(this.currentLanguage);if(!n)return e;const i=this.getNestedValue(n,e);if(void 0===i){const n=this.translations.get(this.options.fallbackLanguage);if(n&&this.currentLanguage!==this.options.fallbackLanguage){const i=this.getNestedValue(n,e);if(void 0!==i)return this.interpolate(i,t)}return e}return this.interpolate(i,t)}getNestedValue(e,t){return t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:void 0,e)}interpolate(e,t){return"string"!=typeof e?e:e.replace(/\{\{(\w+)\}\}/g,(e,n)=>void 0!==t[n]?t[n]:e)}applyTranslations(){var e;document.querySelectorAll("[data-i18n]").forEach(e=>{const t=e.getAttribute("data-i18n"),n=this.t(t),i=e.getAttribute("data-i18n-attr");i?e.setAttribute(i,n):e.textContent=n});const t=null==(e=document.querySelector("title"))?void 0:e.getAttribute("data-i18n");t&&(document.title=this.t(t))}saveLanguagePreference(){localStorage.setItem(this.options.storageKey,this.currentLanguage)}updateURL(){const e=new URL(window.location);e.searchParams.set("lang",this.currentLanguage),window.history.replaceState({},"",e.toString())}updateLanguageSwitcher(){const e=document.querySelector(".current-language");if(e){const t={nl:"Nederlands",en:"English",fr:"Français"};e.textContent=t[this.currentLanguage]}document.querySelectorAll("[data-lang]").forEach(e=>{e.getAttribute("data-lang")===this.currentLanguage?(e.classList.add("active"),e.setAttribute("aria-current","true")):(e.classList.remove("active"),e.removeAttribute("aria-current"))})}triggerLanguageChangeEvent(){const e=new CustomEvent("languagechange",{detail:{language:this.currentLanguage,previousLanguage:this.previousLanguage}});document.dispatchEvent(e)}getCurrentLanguage(){return this.currentLanguage}getAvailableLanguages(){return["nl","en","fr"]}isServiceLoading(){return this.isLoading}preloadLanguages(){return r(this,null,function*(){const e=this.getAvailableLanguages().map(e=>this.loadLanguage(e));try{yield Promise.all(e)}catch(t){}})}}const v=new class{constructor(){this.components=new Map,this.services=new Map,this.isInitialized=!1,this.init()}init(){return r(this,null,function*(){try{yield this.waitForDOM(),yield this.setupServices(),this.initializeComponents(),this.setupGlobalEventListeners(),this.setupAccessibility(),this.setupAnimations(),this.isInitialized=!0,this.announceReady()}catch(e){}})}waitForDOM(){return new Promise(e=>{u(e)})}setupServices(){return r(this,null,function*(){const e=new m({defaultLanguage:"nl",fallbackLanguage:"nl"});yield e.init(),this.services.set("i18n",e);const t=new o(e.getCurrentLanguage());this.services.set("validation",t)})}initializeComponents(){this.initNavigation(),this.initLanguageSwitcher(),this.initContactForm(),this.initScrollEffects(),this.initFAQ()}initNavigation(){const e=g(".header nav");if(e){const t=new c(e,{mobileBreakpoint:768,closeOnOutsideClick:!0,closeOnEscape:!0});this.components.set("navigation",t),this.setActiveNavigation()}}initLanguageSwitcher(){const e=g(".language-switcher");if(e){const t=this.services.get("i18n"),n=new f(e,t);this.components.set("languageSwitcher",n)}}initContactForm(){const e=g("#contactForm");if(e){const t=new l(e,{validateOnBlur:!0,showSuccessMessage:!0,resetOnSuccess:!0,trackAnalytics:!1});this.components.set("contactForm",t)}}initScrollEffects(){this.setupSmoothScrolling(),d()||this.setupScrollAnimations(),this.setupHeaderScrollEffect()}setupSmoothScrolling(){h('a[href^="#"]').forEach(e=>{e.addEventListener("click",t=>{const n=e.getAttribute("href");if("#"===n)return;const i=g(n);i&&(t.preventDefault(),i.scrollIntoView({behavior:"smooth",block:"start"}))})})}setupScrollAnimations(){const e=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(t.target.classList.add("animate-in"),e.unobserve(t.target))})},{threshold:.1,rootMargin:"0px 0px -50px 0px"});h(".fade-in-section, .service-card, .card").forEach(t=>{e.observe(t)})}setupHeaderScrollEffect(){const e=g(".header");if(!e)return;let t=!1;const n=()=>{window.scrollY>100?e.classList.add("scrolled"):e.classList.remove("scrolled"),t=!1};window.addEventListener("scroll",()=>{t||(requestAnimationFrame(n),t=!0)})}initFAQ(){const e=h(".faq-item");e.forEach(t=>{const n=t.querySelector(".faq-question"),i=t.querySelector(".faq-answer");n&&i&&(n.addEventListener("click",()=>{const s=n.classList.contains("active");e.forEach(e=>{const n=e.querySelector(".faq-question"),i=e.querySelector(".faq-answer");e!==t&&(n.classList.remove("active"),i.classList.remove("active"),n.setAttribute("aria-expanded","false"))}),s?(n.classList.remove("active"),i.classList.remove("active"),n.setAttribute("aria-expanded","false")):(n.classList.add("active"),i.classList.add("active"),n.setAttribute("aria-expanded","true"))}),n.setAttribute("aria-expanded","false"),n.setAttribute("role","button"),n.setAttribute("tabindex","0"),n.addEventListener("keydown",e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),n.click())}))})}setupGlobalEventListeners(){window.addEventListener("resize",this.handleResize.bind(this)),document.addEventListener("visibilitychange",this.handleVisibilityChange.bind(this)),window.addEventListener("error",this.handleError.bind(this))}setupAccessibility(){this.setupSkipLinks(),this.setupFocusManagement(),this.setupKeyboardNavigation()}setupSkipLinks(){h(".skip-link").forEach(e=>{e.addEventListener("click",t=>{const n=e.getAttribute("href"),i=g(n);i&&(t.preventDefault(),i.setAttribute("tabindex","-1"),i.focus(),i.scrollIntoView({behavior:"smooth"}))})})}setupFocusManagement(){document.addEventListener("keydown",e=>{"Tab"===e.key&&document.body.classList.add("keyboard-navigation")}),document.addEventListener("mousedown",()=>{document.body.classList.remove("keyboard-navigation")})}setupKeyboardNavigation(){document.addEventListener("keydown",e=>{if(e.altKey&&"m"===e.key){e.preventDefault();const t=g(".nav");if(t){const e=t.querySelector(".nav-link");e&&e.focus()}}if(e.altKey&&"c"===e.key){e.preventDefault();const t=g("#main-content");t&&(t.setAttribute("tabindex","-1"),t.focus())}})}setupAnimations(){const e=document.createElement("style");e.textContent="\n      .animate-in {\n        animation: fadeInUp 0.6s ease-out forwards;\n      }\n      \n      @keyframes fadeInUp {\n        from {\n          opacity: 0;\n          transform: translateY(30px);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0);\n        }\n      }\n      \n      .keyboard-navigation *:focus {\n        outline: 2px solid #0056b3 !important;\n        outline-offset: 2px !important;\n      }\n    ",document.head.appendChild(e)}setActiveNavigation(){const e=this.components.get("navigation");if(e){const t=window.location.pathname;e.setActive(t)}}handleResize(){clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.components.forEach(e=>{"function"==typeof e.handleResize&&e.handleResize()})},250)}handleVisibilityChange(){document.hidden}handleError(e){p("Er is een fout opgetreden. Probeer de pagina te vernieuwen.","assertive")}announceReady(){p("Website geladen en klaar voor gebruik","polite")}getComponent(e){return this.components.get(e)}getService(e){return this.services.get(e)}destroy(){this.components.forEach(e=>{"function"==typeof e.destroy&&e.destroy()}),this.components.clear(),this.services.clear(),this.isInitialized=!1}};"undefined"!=typeof window&&(window.TechSupportApp=v);
