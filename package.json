{"name": "techsupport-pro-website", "version": "2.0.0", "description": "TechSupport Pro - Professional IT Support Website", "type": "module", "main": "src/index.html", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write src/**/*.{js,css,html,scss}", "format:check": "prettier --check src/**/*.{js,css,html,scss}", "optimize-images": "node tools/optimize-images.js", "generate-sitemap": "node tools/generate-sitemap.js", "test": "jest", "test:watch": "jest --watch", "lighthouse": "lighthouse http://localhost:4173 --output=html --output-path=./reports/lighthouse.html", "a11y-test": "pa11y-ci --sitemap http://localhost:4173/sitemap.xml", "clean": "<PERSON><PERSON><PERSON> dist", "serve": "npm run build && npm run preview"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "autoprefixer": "^10.4.0", "babel-jest": "^30.0.2", "cssnano": "^6.0.0", "eslint": "^8.55.0", "imagemin": "^8.0.1", "imagemin-avif": "^0.1.5", "imagemin-webp": "^7.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "lighthouse": "^11.0.0", "pa11y-ci": "^3.0.1", "postcss": "^8.4.0", "prettier": "^3.1.0", "rimraf": "^5.0.0", "sass": "^1.69.0", "terser": "^5.43.1", "vite": "^5.0.0"}, "dependencies": {"intersection-observer": "^0.12.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}